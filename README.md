# 物料帐户分配明细报表转Excel工具

## 功能说明

这个工具可以将物料帐户分配明细的txt格式报表转换为结构化的Excel文件，便于数据分析和处理。

## 文件说明

- `convert_to_excel.py` - 主要的转换脚本
- `requirements.txt` - Python依赖包列表
- `物料帐户分配明细_250825.xlsx` - 生成的Excel文件

## 使用方法

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行转换

```bash
python3 convert_to_excel.py
```

### 3. 查看结果

转换完成后，会在当前目录生成 `物料帐户分配明细_250825.xlsx` 文件。

## Excel文件结构

生成的Excel文件包含三个工作表：

### 1. 报表信息
- 报表日期
- 时间范围
- 币种
- 汇率等基本信息

### 2. 物料明细
- 事务处理日期
- 物料编码
- 物料说明
- 来源类型
- 来源
- 数量
- 单位
- 单位成本
- 事务处理值

### 3. 汇总统计
- 总体统计（总物料种类、总数量、总金额）
- 按物料编码汇总
- 按单位汇总

## 数据来源

本工具解析的原始文件：`Material_account_distribution__250825.txt`

## 技术特点

- 使用正则表达式精确解析报表数据
- 自动处理数字格式（去除千分位逗号）
- 智能列宽调整
- 多维度数据汇总
- 错误处理和异常捕获

## 注意事项

- 确保原始txt文件使用UTF-8编码
- 需要安装pandas和openpyxl库
- 生成的Excel文件可以直接在Excel、Numbers等软件中打开
