# 气体发生器行业中英文对照术语表生成器
# 基于RD2204项目"气体发生器排气过程火焰控制研究"技术背景编制

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

def create_terminology_table():
    """创建气体发生器行业中英文对照术语表"""
    
    # 创建工作簿
    wb = Workbook()
    
    # 删除默认工作表
    wb.remove(wb.active)
    
    # 1. 基础组件术语 (12个)
    basic_components = [
        ["气体发生器", "Gas Generator", "GG", "产生气体用于充气的装置，汽车安全气囊系统的核心组件"],
        ["安全气囊", "Airbag", "AB", "汽车被动安全系统，通过气体发生器快速充气保护乘员"],
        ["点火器", "Igniter", "IGN", "启动气体发生器化学反应的电子点火装置"],
        ["推进剂", "Propellant", "-", "气体发生器内部的化学燃料，燃烧产生气体"],
        ["滤网", "Filter Screen", "-", "过滤燃烧产物，确保输出气体清洁"],
        ["外壳", "Housing/Casing", "-", "气体发生器的外部保护结构"],
        ["喷嘴", "Nozzle", "-", "控制气体流出方向和速度的出口结构"],
        ["密封件", "Seal", "-", "确保气体发生器密封性的橡胶或金属件"],
        ["压力容器", "Pressure Vessel", "PV", "承受内部压力的容器结构"],
        ["排气孔", "Vent Hole", "-", "气体发生器的气体排出通道"],
        ["充气管", "Inflator Tube", "-", "连接气体发生器与安全气囊的气体传输管道"],
        ["燃烧室", "Combustion Chamber", "-", "推进剂燃烧的密闭空间"]
    ]
    
    # 2. 测试设备术语 (12个)
    test_equipment = [
        ["高速相机", "High Speed Camera", "HSC", "用于捕捉快速运动过程的专业摄像设备"],
        ["高速摄像技术", "High Speed Video Technology", "HSV", "利用高帧率相机记录快速过程的技术方法"],
        ["光源系统", "Lighting System", "-", "为高速摄像提供充足照明的设备"],
        ["背景板", "Background Panel", "-", "提供拍摄背景，增强图像对比度的设备"],
        ["工装夹具", "Fixture/Jig", "-", "固定和定位测试样品的专用工具"],
        ["测试台架", "Test Bench", "-", "支撑测试设备和样品的机械结构"],
        ["数据采集系统", "Data Acquisition System", "DAS", "收集和记录测试数据的电子系统"],
        ["压力传感器", "Pressure Sensor", "-", "测量气体压力的传感器设备"],
        ["温度传感器", "Temperature Sensor", "-", "监测温度变化的传感器设备"],
        ["触发系统", "Trigger System", "-", "控制测试启动时机的电子系统"],
        ["同步控制器", "Synchronization Controller", "-", "协调多设备同步工作的控制设备"],
        ["图像处理系统", "Image Processing System", "IPS", "分析和处理高速摄像数据的软硬件系统"]
    ]
    
    # 3. 工艺参数术语 (12个)
    process_parameters = [
        ["帧率", "Frame Rate", "fps", "高速相机每秒拍摄的图像帧数，RD2204项目要求>1500fps"],
        ["曝光时间", "Exposure Time", "-", "相机传感器接收光线的时间长度，RD2204项目优化为666μs"],
        ["分辨率", "Resolution", "-", "图像的像素尺寸，表示图像清晰度"],
        ["拍摄距离", "Shooting Distance", "-", "相机镜头到被摄物体的距离，标准为5英尺"],
        ["照明强度", "Illumination Intensity", "-", "光源提供的照明亮度，RD2204项目使用1000W"],
        ["触发延时", "Trigger Delay", "-", "从触发信号到开始记录的时间间隔"],
        ["记录时长", "Recording Duration", "-", "单次测试的数据记录时间长度"],
        ["采样频率", "Sampling Frequency", "-", "数据采集的频率，单位：赫兹"],
        ["压力范围", "Pressure Range", "-", "测试过程中压力变化的范围"],
        ["温度范围", "Temperature Range", "-", "测试环境或样品的温度变化范围"],
        ["光学倍率", "Optical Magnification", "-", "镜头放大倍数，影响图像细节捕捉"],
        ["景深", "Depth of Field", "DOF", "图像中清晰成像的距离范围"]
    ]
    
    # 4. 火焰控制术语 (12个)
    flame_control = [
        ["火焰形态", "Flame Shape", "-", "燃烧过程中火焰的几何形状特征"],
        ["火焰传播", "Flame Propagation", "-", "火焰在介质中的传播过程和速度"],
        ["燃烧速率", "Burning Rate", "-", "推进剂单位时间内的燃烧量"],
        ["火焰温度", "Flame Temperature", "-", "燃烧过程中火焰的温度"],
        ["燃烧压力", "Combustion Pressure", "-", "燃烧过程中产生的气体压力"],
        ["点火延时", "Ignition Delay", "-", "从点火信号到开始燃烧的时间"],
        ["燃烧完成时间", "Burn Time", "-", "从点火到燃烧结束的总时间"],
        ["气体产生率", "Gas Generation Rate", "-", "单位时间内产生的气体量"],
        ["排气速度", "Exhaust Velocity", "-", "气体从喷嘴排出的速度"],
        ["火焰抑制", "Flame Suppression", "-", "控制或熄灭火焰的技术方法"],
        ["燃烧效率", "Combustion Efficiency", "-", "推进剂完全燃烧的程度"],
        ["火焰稳定性", "Flame Stability", "-", "燃烧过程的稳定性和可控性"]
    ]
    
    # 5. 质量控制术语 (12个)
    quality_control = [
        ["内部工作压力", "Internal Operating Pressure", "IOP", "气体发生器工作时的内部压力"],
        ["压力测试", "Pressure Test", "-", "验证产品承压能力的测试方法"],
        ["密封性测试", "Leak Test", "-", "检验产品密封性能的测试"],
        ["功能测试", "Function Test", "-", "验证产品功能性能的综合测试"],
        ["可靠性测试", "Reliability Test", "-", "评估产品长期可靠性的测试"],
        ["环境测试", "Environmental Test", "-", "在特定环境条件下的产品测试"],
        ["老化测试", "Aging Test", "-", "模拟长期使用条件的加速测试"],
        ["冲击测试", "Impact Test", "-", "评估产品抗冲击能力的测试"],
        ["振动测试", "Vibration Test", "-", "评估产品抗振动能力的测试"],
        ["温度循环测试", "Temperature Cycling Test", "-", "在温度变化条件下的耐久性测试"],
        ["疲劳测试", "Fatigue Test", "-", "评估产品在重复载荷下的耐久性"],
        ["性能验证", "Performance Validation", "-", "确认产品性能符合设计要求的验证"]
    ]
    
    # 6. 安全标准术语 (12个)
    safety_standards = [
        ["爆炸防护", "Explosion Protection", "-", "防止意外爆炸的安全措施"],
        ["安全距离", "Safety Distance", "-", "确保人员安全的最小距离要求"],
        ["防护设备", "Protective Equipment", "PPE", "保护操作人员安全的个人防护用品"],
        ["应急程序", "Emergency Procedure", "-", "紧急情况下的标准化处理流程"],
        ["风险评估", "Risk Assessment", "-", "识别和评估潜在危险的系统方法"],
        ["安全标准", "Safety Standard", "-", "规定安全要求的技术标准"],
        ["操作规程", "Operating Procedure", "SOP", "标准化的操作步骤和要求"],
        ["安全培训", "Safety Training", "-", "提高安全意识和技能的培训"],
        ["事故预防", "Accident Prevention", "-", "预防事故发生的措施和方法"],
        ["安全审核", "Safety Audit", "-", "评估安全管理体系有效性的审核"],
        ["危险识别", "Hazard Identification", "HAZID", "识别潜在危险源的系统方法"],
        ["安全联锁", "Safety Interlock", "-", "防止危险操作的自动安全装置"]
    ]
    
    # 7. 技术规范术语 (12个)
    technical_specs = [
        ["技术规范", "Technical Specification", "Tech Spec", "详细的技术要求和参数说明"],
        ["作业指导书", "Work Instruction", "WI", "具体操作步骤的指导文件，如NEL-WIT-014"],
        ["标准操作程序", "Standard Operating Procedure", "SOP", "标准化的操作流程"],
        ["质量手册", "Quality Manual", "QM", "质量管理体系的指导文件"],
        ["检验标准", "Inspection Standard", "-", "产品检验的技术标准和方法"],
        ["验收标准", "Acceptance Criteria", "-", "产品合格的判定标准"],
        ["技术文件", "Technical Document", "-", "记录技术信息的正式文件"],
        ["设计图纸", "Design Drawing", "-", "产品结构和尺寸的技术图纸"],
        ["工艺流程", "Process Flow", "-", "生产或测试的步骤流程"],
        ["技术报告", "Technical Report", "-", "技术研究或测试结果的报告"],
        ["校准程序", "Calibration Procedure", "-", "设备校准的标准化程序"],
        ["变更控制", "Change Control", "-", "技术文件变更的管理程序"]
    ]
    
    # 8. 专业设备术语 (12个)
    equipment_brands = [
        ["Phantom VEO 710S", "Phantom VEO 710S", "-", "Vision Research公司的高速相机型号，RD2204项目核心设备"],
        ["可调光束光源", "Adjustable Beam Light Source", "-", "功率可调节的专业照明设备，1000W规格"],
        ["光纤HDMI线", "Fiber Optic HDMI Cable", "-", "高清数字信号传输线缆"],
        ["LED照明系统", "LED Lighting System", "-", "发光二极管照明设备"],
        ["温湿度控制设备", "Temperature Humidity Control", "-", "环境条件控制设备"],
        ["数据采集卡", "Data Acquisition Card", "DAQ", "计算机数据采集接口卡"],
        ["信号调理器", "Signal Conditioner", "-", "信号处理和调节设备"],
        ["示波器", "Oscilloscope", "-", "电信号波形显示和分析仪器"],
        ["多通道记录仪", "Multi-channel Recorder", "-", "多路信号同步记录设备"],
        ["压力校准器", "Pressure Calibrator", "-", "压力测量设备的校准仪器"],
        ["高速存储系统", "High Speed Storage System", "-", "用于存储大容量高速摄像数据的存储设备"],
        ["图像分析软件", "Image Analysis Software", "-", "专业的图像处理和分析软件"]
    ]
    
    # 创建各个工作表
    sheets_data = [
        ("基础组件", basic_components),
        ("测试设备", test_equipment),
        ("工艺参数", process_parameters),
        ("火焰控制", flame_control),
        ("质量控制", quality_control),
        ("安全标准", safety_standards),
        ("技术规范", technical_specs),
        ("专业设备", equipment_brands)
    ]
    
    # 设置样式
    header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    content_font = Font(name='微软雅黑', size=10)
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for sheet_name, data in sheets_data:
        ws = wb.create_sheet(title=sheet_name)
        
        # 添加表头
        headers = ["中文术语", "英文术语", "缩写", "技术说明"]
        ws.append(headers)
        
        # 设置表头样式
        for col in range(1, 5):
            cell = ws.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = border
        
        # 添加数据
        for row_data in data:
            ws.append(row_data)
        
        # 设置数据样式
        for row in range(2, len(data) + 2):
            for col in range(1, 5):
                cell = ws.cell(row=row, column=col)
                cell.font = content_font
                cell.border = border
                if col == 4:  # 技术说明列
                    cell.alignment = Alignment(wrap_text=True, vertical='top')
        
        # 调整列宽
        ws.column_dimensions['A'].width = 20  # 中文术语
        ws.column_dimensions['B'].width = 30  # 英文术语
        ws.column_dimensions['C'].width = 10  # 缩写
        ws.column_dimensions['D'].width = 50  # 技术说明
        
        # 设置行高
        for row in range(2, len(data) + 2):
            ws.row_dimensions[row].height = 30
    
    # 创建汇总表
    summary_ws = wb.create_sheet(title="术语汇总", index=0)
    
    # 汇总所有术语
    all_terms = []
    for sheet_name, data in sheets_data:
        for term in data:
            all_terms.append([sheet_name] + term)
    
    # 添加汇总表头
    summary_headers = ["分类", "中文术语", "英文术语", "缩写", "技术说明"]
    summary_ws.append(summary_headers)
    
    # 设置汇总表头样式
    for col in range(1, 6):
        cell = summary_ws.cell(row=1, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = border
    
    # 添加汇总数据
    for term_data in all_terms:
        summary_ws.append(term_data)
    
    # 设置汇总表样式
    for row in range(2, len(all_terms) + 2):
        for col in range(1, 6):
            cell = summary_ws.cell(row=row, column=col)
            cell.font = content_font
            cell.border = border
            if col == 5:  # 技术说明列
                cell.alignment = Alignment(wrap_text=True, vertical='top')
    
    # 调整汇总表列宽
    summary_ws.column_dimensions['A'].width = 12  # 分类
    summary_ws.column_dimensions['B'].width = 20  # 中文术语
    summary_ws.column_dimensions['C'].width = 30  # 英文术语
    summary_ws.column_dimensions['D'].width = 10  # 缩写
    summary_ws.column_dimensions['E'].width = 45  # 技术说明
    
    # 保存文件
    wb.save('气体发生器行业中英文对照术语表_完整版.xlsx')
    print("✅ 气体发生器行业中英文对照术语表已生成完成！")
    print(f"📊 总计收录术语：{len(all_terms)}个")
    print("📁 文件包含以下工作表：")
    for i, (sheet_name, data) in enumerate(sheets_data, 1):
        print(f"   {i}. {sheet_name}（{len(data)}个术语）")
    print("   9. 术语汇总（全部术语）")

if __name__ == "__main__":
    create_terminology_table()
