#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RD2204项目Excel文件解析脚本
解析时间计划和问题清单，生成结构化报告
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import re

def parse_excel_file(file_path):
    """解析Excel文件并提取关键信息"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"成功读取文件: {file_path}")
        print(f"文件形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 清理数据
        df_clean = clean_dataframe(df)
        
        # 提取项目信息
        project_info = extract_project_info(df_clean)
        
        # 提取时间计划
        timeline = extract_timeline(df_clean)
        
        # 提取问题清单
        issues = extract_issues(df_clean)
        
        return {
            'project_info': project_info,
            'timeline': timeline,
            'issues': issues,
            'raw_data': df_clean
        }
        
    except Exception as e:
        print(f"解析文件时出错: {e}")
        return None

def clean_dataframe(df):
    """清理DataFrame数据"""
    # 删除完全为空的行和列
    df_clean = df.dropna(how='all').dropna(axis=1, how='all')
    
    # 重置索引
    df_clean = df_clean.reset_index(drop=True)
    
    return df_clean

def extract_project_info(df):
    """提取项目基本信息"""
    project_info = {}
    
    # 从第一列标题获取项目名称
    if len(df.columns) > 0:
        project_name = str(df.columns[0])
        if '气体发生器排气过程火焰控制研究' in project_name:
            project_info['项目名称'] = '气体发生器排气过程火焰控制研究'
            project_info['项目代号'] = 'RD2204'
    
    return project_info

def extract_timeline(df):
    """提取时间计划信息"""
    timeline = []
    
    # 查找包含序号的列
    serial_col = None
    action_col = None
    status_col = None
    
    for i, col in enumerate(df.columns):
        col_str = str(col)
        if '序号' in col_str:
            serial_col = i
        elif '行动' in col_str:
            action_col = i
        elif '状态' in col_str:
            status_col = i
    
    if serial_col is not None and action_col is not None and status_col is not None:
        for idx, row in df.iterrows():
            serial = row.iloc[serial_col]
            action = row.iloc[action_col]
            status = row.iloc[status_col]
            
            # 检查是否为有效数据行
            if pd.notna(serial) and pd.notna(action) and str(serial).isdigit():
                timeline.append({
                    '序号': serial,
                    '行动': action,
                    '状态': status if pd.notna(status) else '未知'
                })
    
    return timeline

def extract_issues(df):
    """提取问题清单"""
    issues = []
    
    # 查找备注列
    remark_col = None
    for i, col in enumerate(df.columns):
        col_str = str(col)
        if '备注' in col_str:
            remark_col = i
            break
    
    if remark_col is not None:
        for idx, row in df.iterrows():
            remark = row.iloc[remark_col]
            if pd.notna(remark) and str(remark).strip():
                issues.append({
                    '序号': idx + 1,
                    '备注/问题': remark
                })
    
    return issues

def generate_report(parsed_data):
    """生成结构化报告"""
    if not parsed_data:
        return "无法解析文件数据"
    
    report = []
    report.append("=" * 60)
    report.append("RD2204 气体发生器排气过程火焰控制研究项目报告")
    report.append("=" * 60)
    report.append("")
    
    # 项目信息
    if parsed_data['project_info']:
        report.append("📋 项目基本信息")
        report.append("-" * 30)
        for key, value in parsed_data['project_info'].items():
            report.append(f"{key}: {value}")
        report.append("")
    
    # 时间计划
    if parsed_data['timeline']:
        report.append("⏰ 项目时间计划")
        report.append("-" * 30)
        for item in parsed_data['timeline']:
            report.append(f"{item['序号']}. {item['行动']} - {item['状态']}")
        report.append("")
    
    # 问题清单
    if parsed_data['issues']:
        report.append("❓ 问题清单和备注")
        report.append("-" * 30)
        for item in parsed_data['issues']:
            report.append(f"{item['序号']}. {item['备注/问题']}")
        report.append("")
    
    # 数据统计
    report.append("📊 数据统计")
    report.append("-" * 30)
    report.append(f"总任务数: {len(parsed_data['timeline'])}")
    report.append(f"问题/备注数: {len(parsed_data['issues'])}")
    report.append(f"数据行数: {len(parsed_data['raw_data'])}")
    report.append(f"数据列数: {len(parsed_data['raw_data'].columns)}")
    
    return "\n".join(report)

def main():
    """主函数"""
    file_path = 'RD2204 气体发生器排气过程火焰控制研究 - Rocky/会议纪要/RD2204 气体发生器排气过程火焰控制研究 时间计划&问题清单.xlsx'
    
    print("开始解析RD2204项目Excel文件...")
    print("-" * 50)
    
    # 解析文件
    parsed_data = parse_excel_file(file_path)
    
    if parsed_data:
        # 生成报告
        report = generate_report(parsed_data)
        print("\n" + "=" * 60)
        print("解析完成！生成报告如下：")
        print("=" * 60)
        print(report)
        
        # 保存报告到文件
        with open('RD2204项目解析报告.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\n报告已保存到: RD2204项目解析报告.txt")
        
    else:
        print("文件解析失败！")

if __name__ == "__main__":
    main()
