#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RD2204项目Excel文件解析脚本 V2
改进版本，更好地处理数据结构和列识别
"""

import pandas as pd
import numpy as np

def parse_excel_file_v2(file_path):
    """改进版本的Excel文件解析"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"成功读取文件: {file_path}")
        print(f"文件形状: {df.shape}")
        
        # 显示前几行数据以便调试
        print("\n前5行数据:")
        print(df.head())
        
        # 显示列名
        print(f"\n列名: {list(df.columns)}")
        
        # 分析数据结构
        analysis_result = analyze_data_structure(df)
        
        # 提取结构化信息
        extracted_data = extract_structured_data(df, analysis_result)
        
        return extracted_data
        
    except Exception as e:
        print(f"解析文件时出错: {e}")
        return None

def analyze_data_structure(df):
    """分析数据结构，识别关键列"""
    analysis = {
        'serial_col': None,      # 序号列
        'action_col': None,      # 行动列
        'status_col': None,      # 状态列
        'remark_col': None,      # 备注列
        'date_cols': [],         # 日期列
        'data_rows': []          # 数据行
    }
    
    # 分析列名
    for i, col in enumerate(df.columns):
        col_str = str(col)
        print(f"列 {i}: '{col_str}'")
        
        if '序号' in col_str:
            analysis['serial_col'] = i
            print(f"   -> 识别为序号列")
        elif '行动' in col_str:
            analysis['action_col'] = i
            print(f"   -> 识别为行动列")
        elif '状态' in col_str:
            analysis['status_col'] = i
            print(f"   -> 识别为状态列")
        elif '备注' in col_str:
            analysis['remark_col'] = i
            print(f"   -> 识别为备注列")
        elif any(char.isdigit() for char in col_str):
            # 可能是日期列（包含数字）
            analysis['date_cols'].append(i)
            print(f"   -> 可能是日期列")
    
    # 分析数据行
    for idx, row in df.iterrows():
        row_data = {}
        for col_idx, col in enumerate(df.columns):
            value = row.iloc[col_idx]
            if pd.notna(value) and str(value).strip():
                row_data[col_idx] = str(value).strip()
        
        if len(row_data) > 0:
            analysis['data_rows'].append((idx, row_data))
    
    return analysis

def extract_structured_data(df, analysis):
    """提取结构化数据"""
    result = {
        'project_info': {},
        'timeline': [],
        'issues': [],
        'raw_analysis': analysis
    }
    
    # 提取项目信息
    if len(df.columns) > 0:
        project_name = str(df.columns[0])
        if '气体发生器排气过程火焰控制研究' in project_name:
            result['project_info']['项目名称'] = '气体发生器排气过程火焰控制研究'
            result['project_info']['项目代号'] = 'RD2204'
    
    # 提取时间计划
    if analysis['serial_col'] is not None and analysis['action_col'] is not None:
        for idx, row in df.iterrows():
            serial = row.iloc[analysis['serial_col']]
            action = row.iloc[analysis['action_col']]
            status = row.iloc[analysis['status_col']] if analysis['status_col'] is not None else None
            
            # 检查是否为有效数据行
            if (pd.notna(serial) and pd.notna(action) and 
                str(serial).strip() and str(action).strip()):
                
                # 检查序号是否为数字
                serial_str = str(serial).strip()
                if serial_str.isdigit() or serial_str in ['1', '2', '3', '4', '5', '6', '7', '8']:
                    timeline_item = {
                        '序号': serial_str,
                        '行动': str(action).strip(),
                        '状态': str(status).strip() if pd.notna(status) else '未知'
                    }
                    
                    # 尝试提取备注
                    if analysis['remark_col'] is not None:
                        remark = row.iloc[analysis['remark_col']]
                        if pd.notna(remark) and str(remark).strip():
                            timeline_item['备注'] = str(remark).strip()
                    
                    result['timeline'].append(timeline_item)
    
    # 提取问题清单
    if analysis['remark_col'] is not None:
        for idx, row in df.iterrows():
            remark = row.iloc[analysis['remark_col']]
            if pd.notna(remark) and str(remark).strip():
                result['issues'].append({
                    '行号': idx + 1,
                    '备注/问题': str(remark).strip()
                })
    
    return result

def generate_detailed_report(extracted_data):
    """生成详细报告"""
    if not extracted_data:
        return "无法解析文件数据"
    
    report = []
    report.append("=" * 80)
    report.append("RD2204 气体发生器排气过程火焰控制研究项目详细解析报告")
    report.append("=" * 80)
    report.append("")
    
    # 项目信息
    if extracted_data['project_info']:
        report.append("📋 项目基本信息")
        report.append("-" * 40)
        for key, value in extracted_data['project_info'].items():
            report.append(f"{key}: {value}")
        report.append("")
    
    # 时间计划
    if extracted_data['timeline']:
        report.append("⏰ 项目时间计划")
        report.append("-" * 40)
        for item in extracted_data['timeline']:
            report.append(f"{item['序号']}. {item['行动']}")
            report.append(f"   状态: {item['状态']}")
            if '备注' in item:
                report.append(f"   备注: {item['备注']}")
            report.append("")
    else:
        report.append("⏰ 项目时间计划")
        report.append("-" * 40)
        report.append("未找到有效的时间计划数据")
        report.append("")
    
    # 问题清单
    if extracted_data['issues']:
        report.append("❓ 问题清单和备注")
        report.append("-" * 40)
        for item in extracted_data['issues']:
            report.append(f"{item['行号']}. {item['备注/问题']}")
        report.append("")
    
    # 数据结构分析
    report.append("🔍 数据结构分析")
    report.append("-" * 40)
    analysis = extracted_data['raw_analysis']
    report.append(f"序号列位置: {analysis['serial_col']}")
    report.append(f"行动列位置: {analysis['action_col']}")
    report.append(f"状态列位置: {analysis['status_col']}")
    report.append(f"备注列位置: {analysis['remark_col']}")
    report.append(f"可能的日期列: {analysis['date_cols']}")
    report.append(f"有效数据行数: {len(analysis['data_rows'])}")
    report.append("")
    
    # 数据统计
    report.append("📊 数据统计")
    report.append("-" * 40)
    report.append(f"总任务数: {len(extracted_data['timeline'])}")
    report.append(f"问题/备注数: {len(extracted_data['issues'])}")
    
    return "\n".join(report)

def main():
    """主函数"""
    file_path = 'RD2204 气体发生器排气过程火焰控制研究 - Rocky/会议纪要/RD2204 气体发生器排气过程火焰控制研究 时间计划&问题清单.xlsx'
    
    print("开始解析RD2204项目Excel文件 (V2)...")
    print("-" * 60)
    
    # 解析文件
    extracted_data = parse_excel_file_v2(file_path)
    
    if extracted_data:
        # 生成详细报告
        report = generate_detailed_report(extracted_data)
        print("\n" + "=" * 80)
        print("解析完成！生成详细报告如下：")
        print("=" * 80)
        print(report)
        
        # 保存报告到文件
        with open('RD2204项目详细解析报告.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\n详细报告已保存到: RD2204项目详细解析报告.txt")
        
    else:
        print("文件解析失败！")

if __name__ == "__main__":
    main()
