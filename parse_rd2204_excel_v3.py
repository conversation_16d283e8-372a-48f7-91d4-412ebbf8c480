#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RD2204项目Excel文件解析脚本 V3
基于已知数据结构直接提取信息
"""

import pandas as pd
import numpy as np

def parse_excel_file_v3(file_path):
    """第三版Excel文件解析，基于已知数据结构"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"成功读取文件: {file_path}")
        print(f"文件形状: {df.shape}")
        
        # 基于我们之前看到的数据结构，直接提取信息
        extracted_data = extract_data_directly(df)
        
        return extracted_data
        
    except Exception as e:
        print(f"解析文件时出错: {e}")
        return None

def extract_data_directly(df):
    """直接提取数据，基于已知的数据结构"""
    result = {
        'project_info': {},
        'timeline': [],
        'issues': [],
        'raw_data': df
    }
    
    # 提取项目信息
    if len(df.columns) > 0:
        project_name = str(df.columns[0])
        if '气体发生器排气过程火焰控制研究' in project_name:
            result['project_info']['项目名称'] = '气体发生器排气过程火焰控制研究'
            result['project_info']['项目代号'] = 'RD2204'
    
    # 基于我们之前看到的数据结构，直接提取时间计划
    # 从输出可以看到：
    # 第0行：序号、行动、状态、备注等标题
    # 第1行开始：实际数据
    
    # 检查第一行是否为标题行
    if len(df) > 0:
        first_row = df.iloc[0]
        print(f"第一行数据: {list(first_row.values)}")
        
        # 查找标题行中的关键列
        title_row = None
        for idx, row in df.iterrows():
            row_values = [str(val).strip() if pd.notna(val) else '' for val in row.values]
            if '序号' in row_values and '行动' in row_values and '状态' in row_values:
                title_row = idx
                print(f"找到标题行: 第{idx}行")
                break
        
        if title_row is not None:
            # 基于标题行提取数据
            timeline_data = extract_timeline_from_title_row(df, title_row)
            result['timeline'] = timeline_data
            
            # 提取备注/问题
            issues_data = extract_issues_from_title_row(df, title_row)
            result['issues'] = issues_data
    
    return result

def extract_timeline_from_title_row(df, title_row):
    """从标题行提取时间计划数据"""
    timeline = []
    
    # 获取标题行的列索引
    title_values = [str(val).strip() if pd.notna(val) else '' for val in df.iloc[title_row].values]
    
    serial_col = None
    action_col = None
    status_col = None
    remark_col = None
    
    for i, val in enumerate(title_values):
        if '序号' in val:
            serial_col = i
        elif '行动' in val:
            action_col = i
        elif '状态' in val:
            status_col = i
        elif '备注' in val:
            remark_col = i
    
    print(f"列索引 - 序号:{serial_col}, 行动:{action_col}, 状态:{status_col}, 备注:{remark_col}")
    
    # 从标题行之后提取数据
    for idx in range(title_row + 1, len(df)):
        row = df.iloc[idx]
        
        serial = row.iloc[serial_col] if serial_col is not None else None
        action = row.iloc[action_col] if action_col is not None else None
        status = row.iloc[status_col] if status_col is not None else None
        remark = row.iloc[remark_col] if remark_col is not None else None
        
        # 检查是否为有效数据行
        if (pd.notna(serial) and pd.notna(action) and 
            str(serial).strip() and str(action).strip()):
            
            serial_str = str(serial).strip()
            # 检查序号是否为有效数字
            if serial_str.isdigit() or serial_str in ['1', '2', '3', '4', '5', '6', '7', '8']:
                timeline_item = {
                    '序号': serial_str,
                    '行动': str(action).strip(),
                    '状态': str(status).strip() if pd.notna(status) else '未知'
                }
                
                if pd.notna(remark) and str(remark).strip():
                    timeline_item['备注'] = str(remark).strip()
                
                timeline.append(timeline_item)
                print(f"提取时间计划项: {timeline_item}")
    
    return timeline

def extract_issues_from_title_row(df, title_row):
    """从标题行提取问题/备注数据"""
    issues = []
    
    # 获取标题行的列索引
    title_values = [str(val).strip() if pd.notna(val) else '' for val in df.iloc[title_row].values]
    
    remark_col = None
    for i, val in enumerate(title_values):
        if '备注' in val:
            remark_col = i
            break
    
    if remark_col is not None:
        # 从标题行之后提取备注数据
        for idx in range(title_row + 1, len(df)):
            row = df.iloc[idx]
            remark = row.iloc[remark_col]
            
            if pd.notna(remark) and str(remark).strip():
                issues.append({
                    '行号': idx + 1,
                    '备注/问题': str(remark).strip()
                })
                print(f"提取备注: {str(remark).strip()}")
    
    return issues

def generate_final_report(extracted_data):
    """生成最终报告"""
    if not extracted_data:
        return "无法解析文件数据"
    
    report = []
    report.append("=" * 80)
    report.append("RD2204 气体发生器排气过程火焰控制研究项目最终解析报告")
    report.append("=" * 80)
    report.append("")
    
    # 项目信息
    if extracted_data['project_info']:
        report.append("📋 项目基本信息")
        report.append("-" * 40)
        for key, value in extracted_data['project_info'].items():
            report.append(f"{key}: {value}")
        report.append("")
    
    # 时间计划
    if extracted_data['timeline']:
        report.append("⏰ 项目时间计划")
        report.append("-" * 40)
        for item in extracted_data['timeline']:
            report.append(f"{item['序号']}. {item['行动']}")
            report.append(f"   状态: {item['状态']}")
            if '备注' in item:
                report.append(f"   备注: {item['备注']}")
            report.append("")
    else:
        report.append("⏰ 项目时间计划")
        report.append("-" * 40)
        report.append("未找到有效的时间计划数据")
        report.append("")
    
    # 问题清单
    if extracted_data['issues']:
        report.append("❓ 问题清单和备注")
        report.append("-" * 40)
        for item in extracted_data['issues']:
            report.append(f"{item['行号']}. {item['备注/问题']}")
        report.append("")
    
    # 数据统计
    report.append("📊 数据统计")
    report.append("-" * 40)
    report.append(f"总任务数: {len(extracted_data['timeline'])}")
    report.append(f"问题/备注数: {len(extracted_data['issues'])}")
    report.append(f"原始数据行数: {len(extracted_data['raw_data'])}")
    report.append(f"原始数据列数: {len(extracted_data['raw_data'].columns)}")
    
    return "\n".join(report)

def main():
    """主函数"""
    file_path = 'RD2204 气体发生器排气过程火焰控制研究 - Rocky/会议纪要/RD2204 气体发生器排气过程火焰控制研究 时间计划&问题清单.xlsx'
    
    print("开始解析RD2204项目Excel文件 (V3)...")
    print("-" * 60)
    
    # 解析文件
    extracted_data = parse_excel_file_v3(file_path)
    
    if extracted_data:
        # 生成最终报告
        report = generate_final_report(extracted_data)
        print("\n" + "=" * 80)
        print("解析完成！生成最终报告如下：")
        print("=" * 80)
        print(report)
        
        # 保存报告到文件
        with open('RD2204项目最终解析报告.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\n最终报告已保存到: RD2204项目最终解析报告.txt")
        
    else:
        print("文件解析失败！")

if __name__ == "__main__":
    main()
