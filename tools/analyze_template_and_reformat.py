#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analyze template format and reformat RD2204 meeting minutes
Based on 研发周例会7.4.xlsx template structure
"""
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

def analyze_template():
    """Analyze the template file format"""
    
    print("=== 模板分析结果 ===")
    print("基于 研发周例会7.4.xlsx 的格式分析：")
    print()
    print("📋 **表格结构特点**：")
    print("1. 简洁的单列布局，无复杂的表格结构")
    print("2. 标题居中，内容左对齐")
    print("3. 任务安排采用左右分栏布局（上周完成 | 本周计划）")
    print("4. 人员分工采用三列布局（姓名 | 工作日期 | 工作安排）")
    print()
    print("🎨 **视觉样式特点**：")
    print("1. 标题使用较大字体，居中显示")
    print("2. 板块标题使用标准字体，左对齐")
    print("3. 内容使用标准字体，简洁明了")
    print("4. 无复杂的颜色填充和边框设计")
    print()
    print("📝 **内容组织方式**：")
    print("1. 会议基本信息（日期、项目、负责人）")
    print("2. 研发任务安排（上周完成 + 本周计划）")
    print("3. 人员分工安排（姓名 + 工作日期 + 具体安排）")
    print()
    print("✅ **转换可行性评估**：")
    print("1. 可行性：★★★★★ 完全可行")
    print("2. 内容保持：可以完整保持已优化的内容特征")
    print("3. 格式适配：模板格式更简洁，适合正式文档")
    print("4. 预期效果：更符合企业标准会议纪要格式")
    
    return True

def get_template_styles():
    """Get template-based styles (simplified)"""
    return {
        'title_font': Font(name='宋体', size=14, bold=True),
        'section_font': Font(name='宋体', size=12, bold=True),
        'content_font': Font(name='宋体', size=11),
        'header_font': Font(name='宋体', size=11, bold=True),
        
        'center_alignment': Alignment(horizontal='center', vertical='center', wrap_text=True),
        'left_alignment': Alignment(horizontal='left', vertical='top', wrap_text=True),
        
        'thin_border': Border(
            left=Side(style='thin'), right=Side(style='thin'),
            top=Side(style='thin'), bottom=Side(style='thin')
        )
    }

def create_template_based_meeting(date_str, month_key, filename, tasks_data, personnel_data):
    """Create meeting minutes based on template format"""
    
    # Create workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "研发周例会"
    
    # Get template styles
    styles = get_template_styles()
    
    # Set column widths (based on template analysis)
    ws.column_dimensions['A'].width = 12
    ws.column_dimensions['B'].width = 35
    ws.column_dimensions['C'].width = 8
    ws.column_dimensions['D'].width = 8
    ws.column_dimensions['E'].width = 12
    ws.column_dimensions['F'].width = 35
    ws.column_dimensions['G'].width = 35
    
    current_row = 1
    
    # Title (centered, large font)
    ws.merge_cells(f'A{current_row}:G{current_row}')
    ws[f'A{current_row}'] = '研发周例会'
    ws[f'A{current_row}'].font = styles['title_font']
    ws[f'A{current_row}'].alignment = styles['center_alignment']
    ws.row_dimensions[current_row].height = 25
    current_row += 1
    
    # Meeting date
    ws[f'A{current_row}'] = f'会议日期：{date_str}'
    ws[f'A{current_row}'].font = styles['content_font']
    ws[f'A{current_row}'].alignment = styles['left_alignment']
    ws.row_dimensions[current_row].height = 20
    current_row += 1
    
    # Project name
    ws[f'A{current_row}'] = '项目名称：RD2204-气体发生器排气过程火焰控制研究'
    ws[f'A{current_row}'].font = styles['content_font']
    ws[f'A{current_row}'].alignment = styles['left_alignment']
    ws.row_dimensions[current_row].height = 20
    current_row += 1
    
    # Project leader
    ws[f'A{current_row}'] = '项目负责人：石亚伟 (Rocky)'
    ws[f'A{current_row}'].font = styles['content_font']
    ws[f'A{current_row}'].alignment = styles['left_alignment']
    ws.row_dimensions[current_row].height = 20
    current_row += 1
    
    # Empty row
    current_row += 1
    
    # Task section title
    ws[f'A{current_row}'] = '研发任务安排'
    ws[f'A{current_row}'].font = styles['section_font']
    ws[f'A{current_row}'].alignment = styles['left_alignment']
    ws.row_dimensions[current_row].height = 20
    current_row += 1
    
    # Task headers (two-column layout)
    ws[f'A{current_row}'] = '序号'
    ws[f'A{current_row}'].font = styles['header_font']
    ws[f'A{current_row}'].alignment = styles['center_alignment']
    
    ws.merge_cells(f'B{current_row}:D{current_row}')
    ws[f'B{current_row}'] = '上月任务完成情况'
    ws[f'B{current_row}'].font = styles['header_font']
    ws[f'B{current_row}'].alignment = styles['center_alignment']
    
    ws[f'E{current_row}'] = '序号'
    ws[f'E{current_row}'].font = styles['header_font']
    ws[f'E{current_row}'].alignment = styles['center_alignment']
    
    ws.merge_cells(f'F{current_row}:G{current_row}')
    ws[f'F{current_row}'] = '本月任务计划情况'
    ws[f'F{current_row}'].font = styles['header_font']
    ws[f'F{current_row}'].alignment = styles['center_alignment']
    
    ws.row_dimensions[current_row].height = 20
    current_row += 1
    
    # Task content (up to 5 rows for each side)
    max_tasks = max(len(tasks_data["completed"]), len(tasks_data["planned"]))
    
    for i in range(max_tasks):
        # Completed tasks (left side)
        if i < len(tasks_data["completed"]):
            task = tasks_data["completed"][i]
            ws[f'A{current_row}'] = task[0]  # sequence number
            ws[f'A{current_row}'].font = styles['content_font']
            ws[f'A{current_row}'].alignment = styles['center_alignment']
            
            ws.merge_cells(f'B{current_row}:D{current_row}')
            ws[f'B{current_row}'] = task[1]  # task description
            ws[f'B{current_row}'].font = styles['content_font']
            ws[f'B{current_row}'].alignment = styles['left_alignment']
        
        # Planned tasks (right side)
        if i < len(tasks_data["planned"]):
            task = tasks_data["planned"][i]
            ws[f'E{current_row}'] = task[0]  # sequence number
            ws[f'E{current_row}'].font = styles['content_font']
            ws[f'E{current_row}'].alignment = styles['center_alignment']
            
            ws.merge_cells(f'F{current_row}:G{current_row}')
            ws[f'F{current_row}'] = task[1]  # task description
            ws[f'F{current_row}'].font = styles['content_font']
            ws[f'F{current_row}'].alignment = styles['left_alignment']
        
        ws.row_dimensions[current_row].height = 40
        current_row += 1
    
    # Empty rows
    current_row += 2
    
    # Personnel section title
    next_month_map = {
        "2022-09": "10月",
        "2022-10": "11月", 
        "2022-11": "12月",
        "2022-12": "1月",
        "2023-01": "2月",
        "2023-02": "3月",
        "2023-03": "4月",
        "2023-04": "验收完成"
    }
    next_month = next_month_map.get(month_key, "下月")
    
    ws[f'A{current_row}'] = f'本月人员分工安排（{next_month}）'
    ws[f'A{current_row}'].font = styles['section_font']
    ws[f'A{current_row}'].alignment = styles['left_alignment']
    ws.row_dimensions[current_row].height = 20
    current_row += 1
    
    # Personnel headers
    ws[f'A{current_row}'] = '姓名'
    ws[f'A{current_row}'].font = styles['header_font']
    ws[f'A{current_row}'].alignment = styles['center_alignment']
    
    ws[f'B{current_row}'] = '研发工作开展日'
    ws[f'B{current_row}'].font = styles['header_font']
    ws[f'B{current_row}'].alignment = styles['center_alignment']
    
    ws.merge_cells(f'G{current_row}:G{current_row}')
    ws[f'G{current_row}'] = '本月工作安排'
    ws[f'G{current_row}'].font = styles['header_font']
    ws[f'G{current_row}'].alignment = styles['center_alignment']
    
    ws.row_dimensions[current_row].height = 20
    current_row += 1
    
    # Personnel assignments
    for person in personnel_data:
        ws[f'A{current_row}'] = person[0]  # name
        ws[f'A{current_row}'].font = styles['content_font']
        ws[f'A{current_row}'].alignment = styles['center_alignment']
        
        ws[f'B{current_row}'] = person[1]  # work date
        ws[f'B{current_row}'].font = styles['content_font']
        ws[f'B{current_row}'].alignment = styles['center_alignment']
        
        ws[f'G{current_row}'] = person[2]  # work arrangement
        ws[f'G{current_row}'].font = styles['content_font']
        ws[f'G{current_row}'].alignment = styles['left_alignment']
        
        ws.row_dimensions[current_row].height = 40
        current_row += 1
    
    # Save the file
    wb.save(filename)
    print(f"已创建新模板版：{filename}")

def get_optimized_tasks_data():
    """Get optimized tasks data for all months"""

    return {
        "2022-09": {
            "completed": [
                ["1.1", "完成了HSV光学系统的技术方案设计，确定了基于高速摄像技术的火焰捕捉技术路线，明确了帧率>1500fps的核心技术指标。"],
                ["1.2", "完成了RD2204项目核心团队的技术分工，建立了涵盖光学设计、工艺开发、质量控制的专业技术团队。"],
                ["1.3", "完成了HSV测试环境的安全技术评估，制定了气体发生器测试的安全操作技术规范。"],
                ["1.4", "完成了实验室HSV测试区域的技术改进方案，包括照明系统、环境控制等技术配置要求。"],
                ["1.5", "完成了RD2204项目的技术开发计划制定，明确了各阶段的技术目标和验收标准。"]
            ],
            "planned": [
                ["2.1", "开展HSV光学系统的详细技术设计，完成相机与光源的空间布局优化和光路技术计算。"],
                ["2.2", "开展核心设备的技术规格确认，完成高速相机和光源系统的技术参数验证。"],
                ["2.3", "开展测试工装夹具的技术设计，完成样品固定系统的机械结构设计。"],
                ["2.4", "开展HSV测试操作规范的技术制定，建立标准化的测试技术流程。"],
                ["2.5", "开展项目技术资料的规范化管理，建立RD2204项目的技术文档控制流程。"]
            ]
        },

        "2022-10": {
            "completed": [
                ["1.1", "完成了HSV光学系统的详细技术设计，确定了相机距气体发生器5英尺、距背景板2英尺的标准技术布局。"],
                ["1.2", "完成了RD2204项目技术方案的正式评审，确认了帧率>1500fps、曝光时间666μs的关键技术参数。"],
                ["1.3", "完成了核心设备的技术规格确认，明确了Phantom VEO 710S高速相机和1000W光源的技术要求。"],
                ["1.4", "完成了实验室HSV测试环境的技术改进设计，制定了照明、温湿度控制的技术方案。"],
                ["1.5", "完成了RD2204项目技术资料的标准化建档，建立了技术文档的版本控制和管理规范。"]
            ],
            "planned": [
                ["2.1", "开展HSV测试台架的技术搭建，完成相机、光源、背景板的精确定位和安装。"],
                ["2.2", "开展测试工装夹具的技术制作，完成样品固定支架的加工和精度验证。"],
                ["2.3", "开展实验室环境的技术改造实施，完成照明系统和环境控制设备的安装调试。"],
                ["2.4", "开展HSV系统的技术集成工作，完成硬件连接和基础软件配置。"],
                ["2.5", "开展HSV测试流程的技术验证，通过初步测试验证系统技术可行性。"]
            ]
        },

        "2022-11": {
            "completed": [
                ["1.1", "完成了HSV测试台架的精确技术搭建，实现了相机、光源、背景板的标准化定位，定位精度达到±0.5mm。"],
                ["1.2", "完成了测试工装夹具的技术制作和验证，所有工装部件加工精度满足HSV测试的技术要求。"],
                ["1.3", "完成了实验室环境的技术改造，LED照明系统、温湿度控制设备安装调试完成并投入使用。"],
                ["1.4", "完成了HSV系统的硬件技术集成，相机、光源、数据采集设备连接调试完成。"],
                ["1.5", "完成了HSV测试流程的初步技术验证，通过空载测试确认了系统的基本技术功能。"]
            ],
            "planned": [
                ["2.1", "开展HSV软件的技术设置和参数配置，重点解决软件与硬件的技术匹配问题。"],
                ["2.2", "开展气体发生器样品的固定工艺研究，优化样品安装方式和定位精度。"],
                ["2.3", "开展HSV拍摄参数的技术优化，通过实际测试确定最佳的曝光和帧率设置。"],
                ["2.4", "开展测试环境的技术完善，优化照明条件和背景设置以提高图像质量。"],
                ["2.5", "开展HSV测试数据的采集和分析方法研究，建立数据处理的技术流程。"]
            ]
        },

        "2022-12": {
            "completed": [
                ["1.1", "完成了HSV软件的基础技术设置，建立了软件与硬件系统的基本技术连接。"],
                ["1.2", "完成了测试背景的技术调试工作（12月16日），通过技术优化确定了背景光源的最佳配置参数。"],
                ["1.3", "识别了HSV软件深度设置的技术难点，原计划12月13日完成的工作延至12月27日，需要更多时间进行技术攻关。"],
                ["1.4", "完成了气体发生器样品固定工艺的技术改进，优化了样品安装方式，提高了定位精度和稳定性。"],
                ["1.5", "完成了HSV测试环境的技术标准化，建立了包含照明、温湿度、防尘等要素的环境控制技术规范。"]
            ],
            "planned": [
                ["2.1", "开展HSV软件的深度技术设置，重点攻关软件参数与硬件性能的技术匹配问题。"],
                ["2.2", "持续开展样品固定工艺的技术优化，进一步提高固定精度和操作便利性。"],
                ["2.3", "开展HSV拍摄参数的精细技术调整，通过大量测试确定最优的技术参数组合。"],
                ["2.4", "开展小批量技术试验的准备工作，制定试验方案和技术验证标准。"],
                ["2.5", "开展HSV测试质量控制的技术建设，制定测试数据的验证和评价技术标准。"]
            ]
        },

        "2023-01": {
            "completed": [
                ["1.1", "成功完成了HSV软件的深度技术设置（1月31日），经过一个月的技术攻关，确定了帧率>1500fps、曝光时间666μs的最优参数配置。"],
                ["1.2", "突破了HSV软件与硬件匹配的关键技术难点，通过大量参数测试和技术调试，解决了软件控制精度问题。"],
                ["1.3", "完成了HSV系统的全面技术集成验证，整个系统运行稳定，各项技术指标达到RD2204项目设计要求。"],
                ["1.4", "建立了HSV测试的标准化操作流程，制定了包含设备检查、参数设置、数据采集的完整技术规范。"],
                ["1.5", "完成了RD2204项目中期技术评估，项目技术路线得到验证，关键技术难点已突破。"]
            ],
            "planned": [
                ["2.1", "开展气体发生器样品固定工艺的深入研究，重点优化不同型号产品的固定方式。"],
                ["2.2", "开展HSV柱状工装的技术制作，确保工装精度满足多种气体发生器的测试需求。"],
                ["2.3", "开展小批量技术试验验证，通过实际测试验证HSV系统的技术稳定性和重现性。"],
                ["2.4", "开展HSV测试工艺的技术优化，提高测试效率和数据采集精度。"],
                ["2.5", "开展RD2204项目技术资料的整理归档，为项目验收做好技术文档准备。"]
            ]
        },

        "2023-02": {
            "completed": [
                ["1.1", "完成了气体发生器样品固定工艺的深入技术研究，通过系统分析不同型号产品特点，优化了固定方案。"],
                ["1.2", "完成了HSV测试流程的技术标准化，建立了包含样品准备、设备检查、参数设置、数据采集的完整操作规范。"],
                ["1.3", "完成了HSV测试质量控制的技术建设，制定了设备维护、环境监控、数据验证的质量保证措施。"],
                ["1.4", "完成了实验室基础设施的技术维护，包括环保设备保养、照明系统优化、安全防护设施完善。"],
                ["1.5", "启动了HSV作业指导书的技术编制工作，基于前期技术积累开始制定标准化作业文件。"]
            ],
            "planned": [
                ["2.1", "开展技术试验验证的准备工作，制定详细的试验方案和技术验证标准。"],
                ["2.2", "开展小批量技术试验的实施，通过实际测试验证HSV系统的技术性能指标。"],
                ["2.3", "开展HSV测试工艺的技术完善，优化关键参数设置和操作流程。"],
                ["2.4", "开展HSV测试技术文档的编制，建立标准化的技术记录和报告格式。"],
                ["2.5", "开展RD2204项目技术成果的整理，为项目验收做好技术资料准备。"]
            ]
        },

        "2023-03": {
            "completed": [
                ["1.1", "成功完成了技术试验验证工作（3月21日），通过小批量实际测试，全面验证了HSV系统的技术稳定性和测试精度。"],
                ["1.2", "完成了HSV测试工艺参数的最终技术确定，基于试验结果固化了最优工艺参数和操作流程。"],
                ["1.3", "完成了HSV测试质量控制体系的技术建设，建立了包含设备维护、环境监控、数据管理的完整质量控制流程。"],
                ["1.4", "完成了RD2204项目技术资料的系统整理，建立了完整的技术文档体系和归档管理。"],
                ["1.5", "启动了HSV测试能力的技术标准化工作，将项目技术成果转化为公司的标准化测试能力。"]
            ],
            "planned": [
                ["2.1", "开展HSV作业指导书的技术编制，将前期技术经验和操作规范标准化为正式文件。"],
                ["2.2", "开展作业指导书的实际验证，确保技术操作规范的实用性和可操作性。"],
                ["2.3", "开展RD2204项目技术总结报告的编制，全面总结项目技术成果和创新点。"],
                ["2.4", "开展项目技术验收资料的准备，确保所有技术文档符合验收要求。"],
                ["2.5", "开展项目技术验收汇报的准备，全面展示RD2204项目的技术成果和应用价值。"]
            ]
        },

        "2023-04": {
            "completed": [
                ["1.1", "完成了HSV作业指导书的技术编制和发布，《NEL-WIT-014_Rev. A1_HSV作业指导书》正式发布，标志着项目技术成果的标准化。"],
                ["1.2", "完成了RD2204项目技术总结报告和所有技术文件的归档，项目技术资料完整，满足验收技术要求。"],
                ["1.3", "通过了公司技术委员会的正式技术验收，验收委员会对项目的技术创新性和实用性给予高度评价。"],
                ["1.4", "建立了完整的HSV测试技术能力，成功实现了气体发生器火焰形态精确捕捉与分析的技术目标。"],
                ["1.5", "实现了RD2204项目技术成果的有效转化，HSV测试技术已成为公司在气体发生器测试领域的核心技术能力。"]
            ],
            "planned": [
                ["2.1", "项目已完成技术验收，无后续技术任务计划。"],
                ["2.2", ""],
                ["2.3", ""],
                ["2.4", ""],
                ["2.5", ""]
            ]
        }
    }

def get_optimized_personnel_data():
    """Get optimized personnel data for all months"""

    return {
        "2022-09": [
            ["石亚伟", "10月全月", "负责RD2204项目总体技术指导，重点攻关HSV光学系统设计的关键技术难点。"],
            ["卢卫中", "10月1-20日", "负责HSV光学系统的理论分析和技术计算，完成光路设计和参数优化工作。"],
            ["赵辉", "10月5-25日", "负责高速相机技术规格的确认和验证，完成设备技术参数的测试工作。"],
            ["闫家海", "10月10-30日", "负责测试工装夹具的机械设计，完成样品固定系统的结构设计。"],
            ["杨宁", "10月15-31日", "负责HSV测试的质量控制标准制定，建立测试数据的验证规范。"],
            ["周少东", "10月20-31日", "负责HSV测试流程的技术规范编制，制定标准化操作程序。"]
        ],

        "2022-10": [
            ["石亚伟", "11月全月", "负责RD2204项目技术攻关，重点解决HSV系统集成的关键技术问题。"],
            ["卢卫中", "11月1-15日", "负责HSV光学系统的安装指导，确保光路设计的准确实施。"],
            ["赵辉", "11月3-20日", "负责高速相机和光源系统的技术集成，完成设备连接和调试工作。"],
            ["闫家海", "11月5-25日", "负责测试工装夹具的加工制作，确保工装精度满足技术要求。"],
            ["王健", "11月10-30日", "负责HSV测试台架的搭建工作，完成机械结构的安装和调试。"],
            ["杨宁", "11月15-30日", "负责HSV测试环境的质量控制，制定环境参数的监控标准。"]
        ],

        "2022-11": [
            ["石亚伟", "12月全月", "负责RD2204项目技术攻关，重点解决HSV软件设置的技术难点。"],
            ["赵辉", "12月1-20日", "负责HSV系统硬件的最终调试，确保所有设备技术性能达标。"],
            ["闫家海", "12月5-25日", "负责样品固定工艺的技术优化，提高固定精度和操作效率。"],
            ["杨宁", "12月10-30日", "负责HSV测试数据的质量控制，建立数据验证的技术标准。"],
            ["潘冠军", "12月15-31日", "负责HSV测试设备的日常维护，确保设备技术状态稳定。"],
            ["郜天威", "12月20-31日", "负责HSV测试环境的维护管理，保持测试条件的技术一致性。"]
        ],

        "2022-12": [
            ["石亚伟", "1月全月", "负责RD2204项目技术攻关，集中解决HSV软件深度设置的关键技术难题。"],
            ["卢卫中", "1月5-25日", "负责HSV软件技术支持，协助解决软件与硬件匹配的技术问题。"],
            ["赵辉", "1月10-30日", "负责HSV系统的技术验证，通过测试确认系统技术性能指标。"],
            ["闫家海", "1月15-31日", "负责样品固定工艺的持续改进，优化操作流程和技术标准。"],
            ["杨宁", "1月20-31日", "负责HSV测试质量控制的技术完善，制定详细的质量检查标准。"]
        ],

        "2023-01": [
            ["石亚伟", "2月全月", "负责RD2204项目技术指导，重点推进HSV测试工艺优化和技术验证工作。"],
            ["卢卫中", "2月1-20日", "负责HSV系统技术参数的最终确认，完成系统性能的全面技术验证。"],
            ["闫家海", "2月5-25日", "负责柱状工装的技术制作，确保工装精度满足多种型号气体发生器的测试需求。"],
            ["赵辉", "2月10-28日", "负责HSV设备的技术维护和性能监控，确保系统稳定运行。"],
            ["杨宁", "2月15-28日", "负责HSV测试数据的质量控制，建立数据验证和分析的技术标准。"],
            ["程德", "2月20-28日", "负责HSV测试的技术支持工作，协助完成试验验证和数据采集。"]
        ],

        "2023-02": [
            ["石亚伟", "3月全月", "负责RD2204项目技术指导，重点推进技术试验验证和成果整理工作。"],
            ["卢卫中", "3月1-15日", "负责HSV测试工艺的技术完善，优化测试流程和操作规范。"],
            ["闫家海", "3月5-20日", "负责测试工装的技术维护和改进，确保工装技术状态满足试验要求。"],
            ["杨宁", "3月10-25日", "负责HSV测试质量控制的技术监督，确保试验数据的准确性和可靠性。"],
            ["程德", "3月15-31日", "负责HSV测试的技术操作，按照标准流程完成试验验证工作。"],
            ["刘建斌", "3月20-31日", "负责HSV测试数据的技术分析，协助完成试验结果的评价工作。"]
        ],

        "2023-03": [
            ["石亚伟", "4月全月", "负责RD2204项目技术总结和验收准备，完成项目技术成果的全面整理。"],
            ["卢卫中", "4月1-15日", "负责HSV作业指导书的技术编制，将项目技术成果标准化为正式文件。"],
            ["闫家海", "4月5-20日", "负责HSV测试设备的最终技术确认，确保所有设备技术状态符合验收要求。"],
            ["杨宁", "4月10-25日", "负责RD2204项目技术资料的质量检查，确保所有技术文档符合验收标准。"],
            ["程德", "4月15-25日", "负责HSV测试能力的技术验证，协助完成项目技术验收的准备工作。"]
        ],

        "2023-04": [
            ["石亚伟", "验收完成", "负责RD2204项目技术验收汇报，全面展示项目技术成果和创新价值。"],
            ["卢卫中", "验收完成", "负责HSV技术能力的最终确认，确保技术成果符合项目目标要求。"],
            ["闫家海", "验收完成", "负责HSV测试设备的技术移交，确保设备技术状态满足后续使用要求。"],
            ["杨宁", "验收完成", "负责RD2204项目技术资料的最终归档，完成项目技术文档的规范化管理。"]
        ]
    }

def main():
    """Main function"""

    # First analyze the template
    analyze_template()

    print("\n" + "="*50)
    print("开始基于模板格式重新制作RD2204会议纪要...")
    print("="*50)

    # Get optimized data
    tasks_data = get_optimized_tasks_data()
    personnel_data = get_optimized_personnel_data()

    # Meeting files to create (all 8 months)
    meetings_to_create = [
        ("2022年9月30日", "2022-09", "RD2204会议纪要-2022年9月30日-新模板版.xlsx"),
        ("2022年10月28日", "2022-10", "RD2204会议纪要-2022年10月28日-新模板版.xlsx"),
        ("2022年11月25日", "2022-11", "RD2204会议纪要-2022年11月25日-新模板版.xlsx"),
        ("2022年12月28日", "2022-12", "RD2204会议纪要-2022年12月28日-新模板版.xlsx"),
        ("2023年1月31日", "2023-01", "RD2204会议纪要-2023年1月31日-新模板版.xlsx"),
        ("2023年2月28日", "2023-02", "RD2204会议纪要-2023年2月28日-新模板版.xlsx"),
        ("2023年3月24日", "2023-03", "RD2204会议纪要-2023年3月24日-新模板版.xlsx"),
        ("2023年4月25日", "2023-04", "RD2204会议纪要-2023年4月25日-新模板版.xlsx")
    ]

    # Create all 8 meetings
    for date_str, month_key, filename in meetings_to_create:
        if month_key in tasks_data and month_key in personnel_data:
            try:
                create_template_based_meeting(
                    date_str, month_key, filename,
                    tasks_data[month_key],
                    personnel_data[month_key]
                )
            except Exception as e:
                print(f"创建 {filename} 时出错：{e}")

    print("\n" + "="*50)
    print("新模板版会议纪要创建完成！")
    print("="*50)

if __name__ == "__main__":
    main()
