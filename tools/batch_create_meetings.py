#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch create all remaining monthly meeting minutes for RD2204 project
"""
import pandas as pd

def create_meeting_excel(date, filename, tasks, personnel, equipment, summary):
    """Generic function to create meeting minutes Excel"""
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        
        # Meeting header data
        header_data = [
            ["研发例会", "", ""],
            [f"会议日期：{date}", "", ""],
            ["项目名称：RD2204-气体发生器排气过程火焰控制研究", "", ""],
            ["项目负责人：石亚伟 (Rocky)", "", ""],
            ["参会人员：项目核心团队14人全员参加", "", ""],
            ["会议地点：公司研发中心会议室", "", ""],
            ["", "", ""],
            ["研发任务安排", "", ""],
            ["序号", "任务完成情况", "任务计划情况"]
        ]
        
        # Add task data
        task_data = []
        for i, task in enumerate(tasks, 1):
            task_data.append([i, task[0], task[1]])
        task_data.extend([["", "", ""], ["", "", ""], ["人员分工安排", "", ""], ["姓名", "研发工作开展日", "工作安排"]])
        
        # Add personnel data
        personnel_data = []
        for person in personnel:
            personnel_data.append(person)
        personnel_data.extend([["", "", ""], ["设备与物料配置进展", "", ""], ["类别", "本月配置内容", "用途说明"]])
        
        # Add equipment data
        equipment_data = []
        for equip in equipment:
            equipment_data.append(equip)
        equipment_data.extend([["", "", ""], ["", "", ""]])
        
        # Add summary data
        summary_data = [["会议摘要", "", ""]]
        for para in summary:
            summary_data.append([para, "", ""])
            summary_data.append(["", "", ""])
        
        # Combine all data
        all_data = header_data + task_data + personnel_data + equipment_data + summary_data
        
        # Create DataFrame and write to Excel
        df = pd.DataFrame(all_data)
        sheet_name = f"{date[:7]}会议纪要"
        df.to_excel(writer, sheet_name=sheet_name, index=False, header=False)
        
        # Format worksheet
        workbook = writer.book
        worksheet = writer.sheets[sheet_name]
        worksheet.column_dimensions['A'].width = 15
        worksheet.column_dimensions['B'].width = 50
        worksheet.column_dimensions['C'].width = 50
        
        print(f"Excel文件已创建：{filename}")

def create_all_meetings():
    """Create all remaining meeting minutes"""

    # February 2023
    feb_tasks = [
        ["完成了工艺问题的深入调查与研究工作。通过系统分析HSV测试过程中的各个环节，识别并解决了多个影响测试精度的工艺问题。", "启动试运行验证的准备工作，制定详细的验证方案和测试计划。"],
        ["完成了HSV测试流程的标准化优化。建立了包含样品准备、设备检查、参数设置、数据采集等环节的完整操作规范。", "开展小批量试运行验证，通过实际测试验证HSV系统的稳定性和可重现性。"],
        ["建立了完善的质量控制体系。制定了设备维护、环境监控、数据验证等质量保证措施，确保测试结果的可靠性。", "深化HSV测试工艺的研究，优化关键参数设置和操作流程。"],
        ["完成了实验室基础设施的全面升级改造。包括环保设备保养、照明系统优化、安全防护设施完善等多项改进工作。", "建立HSV测试的文档管理体系，制定标准化的记录和报告格式。"],
        ["启动了HSV作业指导书的编制工作。基于前期积累的技术经验和操作规范，开始制定标准化的作业指导文件。", "完成项目技术资料的整理和归档，为项目验收做好文档准备。"]
    ]
    
    feb_personnel = [
        ["石亚伟", "3月", "负责试运行验证的总体指导和技术把关，制定验证标准和评价体系。"],
        ["朱丰海、杨宁", "3月", "负责工艺参数的固化和HSV作业指导书的编制工作。"],
        ["陈立达、潘冠军", "3月", "负责试运行验证的具体执行，按照标准流程进行测试验证。"],
        ["闫家海、王健", "3月", "负责测试工装的最终优化和标准化配置。"],
        ["杨秀秀、周少东", "3月", "负责质量管理体系的实施和项目文档的整理归档。"],
        ["郝天威", "3月", "负责实验室环境的维护和测试设备的状态监控。"]
    ]
    
    feb_equipment = [
        ["工艺优化完成", "完成HSV测试工艺的全面优化，建立了标准化的操作流程和质量控制体系", "确保HSV测试的稳定性和可重现性"],
        ["基础设施升级", "完成实验室基础设施的升级改造，包括环保设备、照明系统、安全设施等", "提供更好的HSV测试环境"],
        ["质量体系建立", "建立完善的质量管理体系，包括设备维护、环境监控、数据验证等", "确保HSV测试结果的可靠性"],
        ["标准化物料", "完成测试耗材、工具、备件的标准化配置和管理，建立完整的物料体系", "保证HSV测试工作的连续性"],
        ["文档体系", "启动HSV作业指导书的编制，建立标准化的文档管理体系", "为项目验收和知识传承奠定基础"]
    ]
    
    feb_summary = [
        "本次会议是项目工艺优化和验证准备的重要会议，标志着RD2204项目从技术攻关阶段向验证应用阶段的成功转换。会议全面回顾了2月份的工艺问题调查与研究工作，确认了HSV测试流程的标准化优化成果。",
        "会议重点讨论了工艺问题调查的成果。项目团队通过系统分析HSV测试过程中的各个环节，识别并解决了多个影响测试精度的关键问题，建立了更加完善的质量控制体系。这些工艺优化成果为后续的试运行验证奠定了坚实基础。",
        "Rocky在会上强调，项目已经进入关键的验证阶段，所有工作都要以验收标准为导向，确保每个环节都达到项目要求。特别是要注重标准化建设，将前期积累的技术经验和操作规范及时固化，形成可复制、可推广的标准化能力。",
        "会议确定了3月份的工作重点：一是开展试运行验证工作，全面验证HSV系统的稳定性和可靠性；二是完成工艺参数的最终固化；三是启动HSV作业指导书的编制工作。项目即将进入最后的冲刺阶段，各项工作要精益求精。"
    ]

    # Create February meeting
    create_meeting_excel("2023年2月28日", "RD2204会议纪要-2023年2月28日.xlsx", feb_tasks, feb_personnel, feb_equipment, feb_summary)

    # March 2023
    mar_tasks = [
        ["成功完成了试运行验证工作（3月21日完成）。通过小批量的实际测试，全面验证了HSV系统的稳定性、可重现性和测试精度，确认系统达到设计要求。", "启动HSV作业指导书的编制工作，将前期积累的技术经验和操作规范标准化。"],
        ["完成了工艺参数的最终固化工作。基于试运行验证的结果，确定了HSV测试的最优工艺参数和操作流程，形成了标准化的技术规范。", "开展作业操作指导的实际运行与最终确认，确保操作规范的实用性和可操作性。"],
        ["建立了完善的HSV测试质量管理体系。制定了包含设备维护、环境监控、数据管理在内的完整质量控制流程。", "准备项目总结报告的初步框架，整理项目技术成果和创新点。"],
        ["完成了项目技术资料的系统整理和归档。建立了完整的技术文档体系，为项目验收和后续推广奠定了基础。", "完成项目验收资料的准备工作，确保所有文档符合验收要求。"],
        ["启动了HSV测试能力的标准化建设。将项目成果转化为公司的标准化测试能力，实现了技术成果的有效转化。", "准备项目验收汇报材料，全面展示项目的技术成果和创新价值。"]
    ]

    mar_personnel = [
        ["石亚伟", "4月", "主导项目总结报告的撰写，并审核所有标准化文件，准备项目验收汇报。"],
        ["朱丰海、周少东", "4月", "负责HSV作业指导书的编写、审核与发布流程，确保文档质量。"],
        ["陈立达、潘冠军", "4月", "配合进行HSV作业指导书的实际运行验证，并提供操作反馈。"],
        ["杨秀秀", "4月", "负责项目验收资料的整理和归档，确保文档的完整性和规范性。"],
        ["杨宁", "4月", "负责质量管理体系的最终完善和验收准备工作。"],
        ["全体成员", "4月", "参与项目验收准备，配合完成各项验收工作。"]
    ]

    mar_equipment = [
        ["试运行验证", "完成HSV系统的全面试运行验证，确认系统稳定性和测试精度达到要求", "验证HSV测试能力的可靠性"],
        ["工艺固化", "完成HSV测试工艺参数的最终固化，建立标准化的操作规范", "确保HSV测试的一致性和可重现性"],
        ["质量体系", "建立完善的HSV测试质量管理体系，包含全流程的质量控制措施", "保证HSV测试结果的可靠性"],
        ["文档体系", "完成项目技术资料的系统整理，建立完整的文档管理体系", "为项目验收和知识传承提供支撑"],
        ["标准化建设", "启动HSV测试能力的标准化建设，实现技术成果的有效转化", "建立可复制可推广的HSV测试能力"]
    ]

    mar_summary = [
        "本次会议是项目验证阶段的总结会议，标志着RD2204项目所有技术攻关和测试验证工作的圆满完成。会议确认，通过3月份的集中试运行验证，HSV系统已成功固化了一套稳定可靠的火焰测试工艺规范和操作流程。",
        "会议重点回顾了试运行验证的成果。通过小批量的实际测试，全面验证了HSV系统在不同条件下的稳定性和可重现性，测试精度完全满足项目要求。这标志着项目的核心技术目标已经实现，为项目的成功验收奠定了坚实基础。",
        "Rocky在会上宣布，项目正式由研发验证阶段转向成果转化与标准化阶段。项目团队要将前期积累的技术经验和操作规范及时固化，形成完整的标准化文档体系，确保项目成果能够有效转化为公司的核心技术能力。",
        "会议确定了4月份的工作重点：一是完成HSV作业指导书的编制和发布；二是完成项目总结报告的撰写；三是准备项目验收汇报材料。项目即将迎来最终验收，各项工作要做到精益求精，确保项目圆满收官。"
    ]

    # Create March meeting
    create_meeting_excel("2023年3月24日", "RD2204会议纪要-2023年3月24日.xlsx", mar_tasks, mar_personnel, mar_equipment, mar_summary)

    # April 2023 - Project Acceptance
    apr_tasks = [
        ["完成了HSV作业指导书的编制与发布工作。《NEL-WIT-014_Rev. A1_HSV作业指导书》已正式发布，标志着项目技术成果的标准化固化。", "项目已完成，无后续任务计划。"],
        ["完成了项目总结报告和所有过程文件的归档工作。项目技术资料完整，文档体系规范，满足验收要求。", ""],
        ["通过了公司技术委员会的正式验收。验收委员会对项目的技术创新性、实用性和标准化建设给予了高度评价。", ""],
        ["建立了完整的HSV测试能力。项目成功实现了通过高速摄像技术进行气体发生器火焰形态捕捉与分析的技术目标。", ""],
        ["实现了项目成果的有效转化。HSV测试能力已成为公司在气体发生器测试领域的核心技术优势。", ""]
    ]

    apr_personnel = [
        ["石亚伟", "验收日", "主持项目验收汇报，全面展示项目技术成果和创新价值。"],
        ["全体成员", "验收日", "参加项目验收会，回答技术委员会的质询，展示团队协作成果。"],
        ["朱丰海", "验收日", "汇报HSV作业指导书的编制情况和标准化建设成果。"],
        ["杨秀秀", "验收日", "汇报项目文档管理和质量控制体系建设情况。"],
        ["技术团队", "验收日", "展示HSV系统的技术特点和测试能力。"],
        ["质量团队", "验收日", "汇报项目质量管理和风险控制情况。"]
    ]

    apr_equipment = [
        ["HSV测试系统", "建立了完整的HSV火焰捕捉与分析系统，实现了项目的核心技术目标", "为公司提供了先进的气体发生器测试能力"],
        ["标准化文档", "完成了HSV作业指导书等标准化文档的编制，建立了完整的技术文档体系", "确保HSV技术的可复制和可推广"],
        ["质量体系", "建立了完善的HSV测试质量管理体系，确保测试结果的可靠性和一致性", "保证HSV测试能力的持续稳定"],
        ["技术成果", "实现了HSV技术在气体发生器测试领域的成功应用，填补了相关技术空白", "提升了公司的技术竞争力"],
        ["人才培养", "培养了一支掌握HSV技术的专业团队，积累了丰富的技术经验", "为公司技术发展提供了人才保障"]
    ]

    apr_summary = [
        "本次会议为RD2204项目的正式验收会议，标志着项目的圆满完成。石亚伟（Rocky）代表项目组向公司技术委员会做了全面的工作汇报，系统展示了项目的技术成果、创新价值和应用前景。",
        "验收委员会认真审阅了包括《项目总结报告》、《HSV作业指导书》、火焰分析数据及标准化文件在内的全部成果资料。委员会对项目所建立的高速摄像观测与分析能力给予了高度评价，认为项目技术路线正确，创新性突出，实用价值显著。",
        "经过充分讨论和技术质询，验收委员会一致认为RD2204项目的研发工作是成功的，项目目标全面实现，技术成果达到预期要求。项目建立的HSV测试能力具有高抗干扰性、操作简便、安全可靠的特点，为公司在气体发生器测试领域建立了核心竞争优势。",
        "验收委员会正式通过了RD2204项目的技术验收，标志着项目的圆满成功。项目成果将为公司后续的产品开发和技术创新提供重要支撑，具有重要的战略意义和应用价值。"
    ]

    # Create April meeting
    create_meeting_excel("2023年4月25日", "RD2204会议纪要-2023年4月25日.xlsx", apr_tasks, apr_personnel, apr_equipment, apr_summary)

if __name__ == "__main__":
    create_all_meetings()
