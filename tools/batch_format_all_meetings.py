#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch format all RD2204 meeting minutes Excel files to professional standard
"""
import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
import os
import glob

def apply_professional_formatting(input_file, output_file):
    """Apply professional formatting to an existing Excel file"""
    
    # Load existing workbook
    wb = openpyxl.load_workbook(input_file)
    ws = wb.active
    
    # Define professional styles
    title_font = Font(name='微软雅黑', size=16, bold=True, color='FFFFFF')
    header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
    section_font = Font(name='微软雅黑', size=11, bold=True, color='2F4F4F')
    content_font = Font(name='微软雅黑', size=10, color='000000')
    
    title_fill = PatternFill(start_color='2F4F4F', end_color='2F4F4F', fill_type='solid')
    header_fill = PatternFill(start_color='4682B4', end_color='4682B4', fill_type='solid')
    section_fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
    
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    left_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
    
    # Set column widths
    ws.column_dimensions['A'].width = 18
    ws.column_dimensions['B'].width = 55
    ws.column_dimensions['C'].width = 55
    
    # Get data range
    max_row = ws.max_row
    
    # Format title (first row with content)
    title_row = 1
    if ws[f'A{title_row}'].value and '研发例会' in str(ws[f'A{title_row}'].value):
        ws.merge_cells(f'A{title_row}:C{title_row}')
        ws[f'A{title_row}'].value = '研发例会纪要'
        ws[f'A{title_row}'].font = title_font
        ws[f'A{title_row}'].fill = title_fill
        ws[f'A{title_row}'].alignment = center_alignment
        ws[f'A{title_row}'].border = thin_border
        ws.row_dimensions[title_row].height = 30
    
    # Format meeting info section
    for row in range(2, max_row + 1):
        cell_value = str(ws[f'A{row}'].value) if ws[f'A{row}'].value else ''
        
        # Meeting basic info (date, project name, etc.)
        if any(keyword in cell_value for keyword in ['会议日期', '项目名称', '项目负责人', '参会人员', '会议地点']):
            ws.merge_cells(f'A{row}:C{row}')
            ws[f'A{row}'].font = content_font
            ws[f'A{row}'].alignment = left_alignment
            ws[f'A{row}'].border = thin_border
            ws.row_dimensions[row].height = 20
        
        # Section headers
        elif any(keyword in cell_value for keyword in ['研发任务安排', '人员分工安排', '设备与物料配置', '会议摘要']):
            ws.merge_cells(f'A{row}:C{row}')
            ws[f'A{row}'].font = section_font
            ws[f'A{row}'].fill = section_fill
            ws[f'A{row}'].alignment = center_alignment
            ws[f'A{row}'].border = thin_border
            ws.row_dimensions[row].height = 25
        
        # Table headers (序号, 姓名, 类别 etc.)
        elif any(keyword in cell_value for keyword in ['序号', '姓名', '类别']):
            for col in range(1, 4):
                cell = ws.cell(row=row, column=col)
                if cell.value:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment
                    cell.border = thin_border
            ws.row_dimensions[row].height = 25
        
        # Content rows
        elif ws[f'A{row}'].value and str(ws[f'A{row}'].value).strip():
            for col in range(1, 4):
                cell = ws.cell(row=row, column=col)
                if cell.value:
                    cell.font = content_font
                    cell.alignment = left_alignment if col > 1 else center_alignment
                    cell.border = thin_border
            
            # Set appropriate row height based on content length
            max_content_length = max([len(str(ws.cell(row=row, column=col).value or '')) for col in range(1, 4)])
            if max_content_length > 200:
                ws.row_dimensions[row].height = 80
            elif max_content_length > 100:
                ws.row_dimensions[row].height = 60
            elif max_content_length > 50:
                ws.row_dimensions[row].height = 40
            else:
                ws.row_dimensions[row].height = 25
        
        # Empty rows
        else:
            ws.row_dimensions[row].height = 10
    
    # Save formatted file
    wb.save(output_file)
    print(f"已格式化：{output_file}")

def batch_format_all_meetings():
    """Batch format all meeting minutes files"""
    
    # Find all meeting minutes files
    meeting_files = glob.glob('RD2204会议纪要-*.xlsx')
    meeting_files = [f for f in meeting_files if '专业格式' not in f]  # Exclude already formatted files
    meeting_files.sort()
    
    print(f"找到 {len(meeting_files)} 个会议纪要文件需要格式化：")
    for file in meeting_files:
        print(f"  - {file}")
    
    print("\n开始批量格式化...")
    
    for input_file in meeting_files:
        # Generate output filename
        base_name = input_file.replace('.xlsx', '')
        output_file = f"{base_name}-专业格式.xlsx"
        
        try:
            apply_professional_formatting(input_file, output_file)
        except Exception as e:
            print(f"格式化 {input_file} 时出错：{e}")
    
    print(f"\n批量格式化完成！共处理 {len(meeting_files)} 个文件。")
    
    # List all formatted files
    formatted_files = glob.glob('RD2204会议纪要-*-专业格式.xlsx')
    formatted_files.sort()
    print(f"\n已生成的专业格式文件：")
    for file in formatted_files:
        print(f"  ✅ {file}")

if __name__ == "__main__":
    batch_format_all_meetings()
