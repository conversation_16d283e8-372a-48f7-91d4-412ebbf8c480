#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch optimize all RD2204 meeting minutes based on confirmed standards
Step 2 & 3: Standardize and batch process remaining 6 files
"""
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

# Standardized personnel data based on timesheet
PERSONNEL_DATA = {
    "2022-11": ["石亚伟", "周少东", "杨秀秀", "卢卫中", "闫家海", "陈立达", "杨宁", "赵辉", "潘冠军", "郜天威"],
    "2022-12": ["石亚伟", "周少东", "杨秀秀", "卢卫中", "闫家海", "陈立达", "杨宁", "赵辉", "潘冠军", "郜天威"],
    "2023-01": ["石亚伟", "周少东", "闫家海", "赵辉", "卢卫中", "杨秀秀", "陈立达", "杨宁", "潘冠军", "郜天威", "程德", "刘建斌"],
    "2023-02": ["石亚伟", "周少东", "杨秀秀", "卢卫中", "闫家海", "陈立达", "杨宁", "赵辉", "潘冠军", "郜天威", "程德", "刘建斌"],
    "2023-03": ["石亚伟", "杨秀秀", "卢卫中", "闫家海", "陈立达", "杨宁", "赵辉", "潘冠军", "郜天威", "程德", "刘建斌"],
    "2023-04": ["石亚伟", "卢卫中", "闫家海", "杨宁", "程德", "刘建斌"]
}

def get_standardized_styles():
    """Get standardized professional styles"""
    return {
        'title_font': Font(name='微软雅黑', size=16, bold=True, color='FFFFFF'),
        'header_font': Font(name='微软雅黑', size=12, bold=True, color='FFFFFF'),
        'section_font': Font(name='微软雅黑', size=11, bold=True, color='2F4F4F'),
        'content_font': Font(name='微软雅黑', size=10, color='000000'),
        'title_fill': PatternFill(start_color='2F4F4F', end_color='2F4F4F', fill_type='solid'),
        'header_fill': PatternFill(start_color='4682B4', end_color='4682B4', fill_type='solid'),
        'section_fill': PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid'),
        'thin_border': Border(
            left=Side(style='thin'), right=Side(style='thin'),
            top=Side(style='thin'), bottom=Side(style='thin')
        ),
        'center_alignment': Alignment(horizontal='center', vertical='center', wrap_text=True),
        'left_alignment': Alignment(horizontal='left', vertical='top', wrap_text=True)
    }

def get_month_tasks(month_key):
    """Get standardized tasks for each month"""
    
    tasks_data = {
        "2022-11": {
            "completed": [
                ["1.1", "完成了HSV光学系统的总体技术设计，确定了相机距气体发生器5英尺、距有机玻璃背景板2英尺的标准技术布局。通过理论计算和仿真分析，验证了光学系统的技术可行性。", "光学系统技术设计完成"],
                ["1.2", "完成了核心设备的技术规格确认和技术匹配性验证，明确了设备技术要求和接口标准。建立了设备技术验证的标准流程。", "设备技术规格确认"],
                ["1.3", "完成了测试工装夹具的技术制作和精度验证，确保工装技术指标满足测试要求。建立了工装技术质量控制体系。", "工装技术制作完成"],
                ["1.4", "完成了实验室环境的技术改造实施，包括照明系统、环境控制等技术升级。建立了环境技术监控体系。", "实验室技术改造完成"],
                ["1.5", "完成了HSV测试流程的技术标准化，建立了详细的技术操作规范和质量控制要点。", "测试技术标准化建立"]
            ],
            "planned": [
                ["2.1", "开展HSV测试台架的技术搭建工作，完成相机、光源、背景板的精确技术定位和安装调试。", "测试台架技术搭建"],
                ["2.2", "开展实验室环境的技术优化完善，包括照明系统、温湿度控制、安全防护等设施的技术调试。", "环境技术优化"],
                ["2.3", "开展HSV系统的技术集成工作，完成硬件连接和基础软件技术配置。", "系统技术集成"],
                ["2.4", "开展HSV测试的质量管理体系技术建设，制定设备维护、数据管理、文档控制等技术规范。", "质量管理技术体系"],
                ["2.5", "开展项目中期技术评估和风险分析，制定后续阶段的详细技术实施计划。", "技术评估与规划"]
            ]
        },
        
        "2022-12": {
            "completed": [
                ["1.1", "完成了HSV测试台架的精确技术搭建工作，实现了相机、光源、背景板的精确技术定位。通过反复技术调试，确保了光学系统的最佳技术配置。", "测试台架技术搭建完成"],
                ["1.2", "完成了测试背景的技术调试工作（12月16日完成）。通过与技术合作伙伴的远程技术讨论，确定了背景光源的最佳技术参数配置。", "测试背景技术调试完成"],
                ["1.3", "技术难点识别：HSV软件技术设置工作比原计划出现延迟（原计划12月13日完成，实际12月27日开始）。经技术分析，主要原因是软件技术参数复杂，需要更多时间进行技术优化。", "软件技术设置延迟分析"],
                ["1.4", "完成了测试工装夹具的最终技术调试和精度验证。所有工装部件技术运行稳定，定位精度满足HSV测试技术要求。", "工装技术调试完成"],
                ["1.5", "建立了完善的HSV测试环境技术控制体系，包括照明、温湿度、防尘等环境因素的技术标准化管理。", "环境技术控制体系建立"]
            ],
            "planned": [
                ["2.1", "开展HSV软件的深度技术设置和参数优化工作，重点解决软件与硬件的技术匹配问题。", "软件技术深度设置"],
                ["2.2", "持续进行工艺问题的技术调查与研究，优化样品固定方式和拍摄参数技术设置。", "工艺技术问题研究"],
                ["2.3", "完成HSV软件的最终技术设置与验证工作，确保软件技术参数与项目技术要求完全匹配。", "软件技术最终验证"],
                ["2.4", "准备并开展小批量的技术试运行，验证整个HSV测试流程的技术稳定性。", "技术试运行准备"],
                ["2.5", "建立HSV测试的完整技术质量管理体系，制定详细的技术操作规范和验收标准。", "技术质量管理体系"]
            ]
        },
        
        "2023-01": {
            "completed": [
                ["1.1", "成功完成了HSV软件的最终技术设置与验证工作（1月31日完成）。经过一个月的深度技术调试，确定了帧率>1500fps、曝光时间666μs的最优技术参数配置。", "软件技术设置突破"],
                ["1.2", "突破了HSV软件技术设置的关键技术难点。通过与技术合作伙伴的深度技术交流和大量的参数技术测试，解决了软件与硬件技术匹配的复杂问题。", "关键技术难点突破"],
                ["1.3", "完成了HSV系统的全面技术集成和初步验证。整个系统技术运行稳定，各项技术指标达到设计要求，为后续技术测试奠定了坚实基础。", "系统技术集成完成"],
                ["1.4", "建立了HSV测试的标准化技术操作流程。制定了详细的技术测试步骤、参数设置、数据记录等技术操作规范，确保测试结果的技术一致性。", "技术操作流程标准化"],
                ["1.5", "完成了项目中期技术评估和风险分析。项目技术路线得到验证，关键技术难点已突破，为项目成功奠定了技术基础。", "中期技术评估完成"]
            ],
            "planned": [
                ["2.1", "开展工艺问题的深入技术调查与研究工作，重点优化样品固定方式和测试流程技术标准化。", "工艺技术深入研究"],
                ["2.2", "完成HSV柱状工装的技术制作和验证，确保工装技术精度满足不同型号气体发生器的测试技术需求。", "柱状工装技术制作"],
                ["2.3", "开展小批量技术试运行验证工作，通过实际技术测试验证HSV系统的技术稳定性和可靠性。", "技术试运行验证"],
                ["2.4", "深化工艺问题技术研究，优化测试环境技术控制和样品处理技术流程，提高测试技术精度和效率。", "工艺技术深化研究"],
                ["2.5", "建立完善的技术质量管理体系和文档控制技术流程，为项目验收做好技术准备。", "技术质量管理完善"]
            ]
        },

        "2023-02": {
            "completed": [
                ["1.1", "完成了工艺问题的深入技术调查与研究工作。通过系统技术分析HSV测试过程中的各个环节，识别并解决了多个影响测试技术精度的工艺问题。", "工艺技术问题解决"],
                ["1.2", "完成了HSV测试流程的技术标准化优化。建立了包含样品准备、设备检查、参数设置、数据采集等环节的完整技术操作规范。", "测试技术流程优化"],
                ["1.3", "建立了完善的技术质量控制体系。制定了设备维护、环境监控、数据验证等技术质量保证措施，确保测试结果的技术可靠性。", "技术质量控制体系"],
                ["1.4", "完成了实验室基础设施的全面技术升级改造。包括环保设备技术保养、照明系统技术优化、安全防护设施技术完善等多项技术改进工作。", "基础设施技术升级"],
                ["1.5", "启动了HSV作业指导书的技术编制工作。基于前期积累的技术经验和操作规范，开始制定标准化的技术作业指导文件。", "技术文档编制启动"]
            ],
            "planned": [
                ["2.1", "开展技术试运行验证的准备工作，制定详细的技术验证方案和测试计划。", "技术试运行准备"],
                ["2.2", "开展小批量技术试运行验证，通过实际技术测试验证HSV系统的技术稳定性和可重现性。", "技术试运行执行"],
                ["2.3", "深化HSV测试工艺的技术研究，优化关键技术参数设置和操作流程。", "工艺技术深化"],
                ["2.4", "建立HSV测试的技术文档管理体系，制定标准化的技术记录和报告格式。", "技术文档管理"],
                ["2.5", "完成项目技术资料的整理和归档，为项目验收做好技术文档准备。", "技术资料整理"]
            ]
        },

        "2023-03": {
            "completed": [
                ["1.1", "成功完成了技术试运行验证工作（3月21日完成）。通过小批量的实际技术测试，全面验证了HSV系统的技术稳定性、可重现性和测试技术精度，确认系统达到技术设计要求。", "技术试运行验证完成"],
                ["1.2", "完成了工艺参数的最终技术固化工作。基于试运行验证的技术结果，确定了HSV测试的最优工艺技术参数和操作流程，形成了标准化的技术规范。", "工艺技术参数固化"],
                ["1.3", "建立了完善的HSV测试技术质量管理体系。制定了包含设备维护、环境监控、数据管理在内的完整技术质量控制流程。", "技术质量管理体系"],
                ["1.4", "完成了项目技术资料的系统整理和归档。建立了完整的技术文档体系，为项目验收和后续技术推广奠定了基础。", "技术资料系统整理"],
                ["1.5", "启动了HSV测试能力的技术标准化建设。将项目技术成果转化为公司的标准化测试技术能力，实现了技术成果的有效转化。", "技术标准化建设"]
            ],
            "planned": [
                ["2.1", "开展HSV作业指导书的技术编制工作，将前期积累的技术经验和操作规范标准化。", "技术作业指导书编制"],
                ["2.2", "开展作业操作指导的实际技术运行与最终确认，确保技术操作规范的实用性和可操作性。", "技术操作指导确认"],
                ["2.3", "准备项目技术总结报告的初步框架，整理项目技术成果和创新点。", "技术总结报告准备"],
                ["2.4", "完成项目技术验收资料的准备工作，确保所有技术文档符合验收要求。", "技术验收资料准备"],
                ["2.5", "准备项目技术验收汇报材料，全面展示项目的技术成果和创新价值。", "技术验收汇报准备"]
            ]
        },

        "2023-04": {
            "completed": [
                ["1.1", "完成了HSV作业指导书的技术编制与发布工作。《NEL-WIT-014_Rev. A1_HSV作业指导书》已正式发布，标志着项目技术成果的标准化固化。", "技术作业指导书完成"],
                ["1.2", "完成了项目技术总结报告和所有过程技术文件的归档工作。项目技术资料完整，技术文档体系规范，满足验收技术要求。", "技术文档归档完成"],
                ["1.3", "通过了公司技术委员会的正式技术验收。验收委员会对项目的技术创新性、实用性和标准化建设给予了高度技术评价。", "技术验收通过"],
                ["1.4", "建立了完整的HSV测试技术能力。项目成功实现了通过高速摄像技术进行气体发生器火焰形态捕捉与分析的技术目标。", "技术能力建立完成"],
                ["1.5", "实现了项目技术成果的有效转化。HSV测试技术能力已成为公司在气体发生器测试技术领域的核心技术优势。", "技术成果转化完成"]
            ],
            "planned": [
                ["2.1", "项目已完成，无后续技术任务计划。", "项目技术完成"],
                ["2.2", "", ""],
                ["2.3", "", ""],
                ["2.4", "", ""],
                ["2.5", "", ""]
            ]
        }
    }
    
    return tasks_data.get(month_key, {"completed": [], "planned": []})

def get_month_personnel(month_key):
    """Get standardized personnel assignments for each month"""
    
    personnel_list = PERSONNEL_DATA.get(month_key, [])
    next_month_map = {
        "2022-11": "12月",
        "2022-12": "1月", 
        "2023-01": "2月",
        "2023-02": "3月",
        "2023-03": "4月",
        "2023-04": "验收日"
    }
    next_month = next_month_map.get(month_key, "下月")
    
    # 石亚伟 always has assignments as team leader
    assignments = [
        ["石亚伟", next_month, f"负责项目总体技术指导和关键技术难点攻关，制定{next_month}技术开发计划和质量控制技术标准。"]
    ]
    
    # Add other personnel based on availability and roles
    for person in personnel_list[1:]:
        if person == "卢卫中":
            assignments.append([person, next_month, "负责HSV光学系统的技术设计和理论分析，完成光学参数技术优化和系统集成技术方案。"])
        elif person == "朱丰海":
            assignments.append([person, next_month, "负责HSV测试工艺的技术开发，制定标准化技术操作流程和质量控制技术要点。"])
        elif person == "赵辉":
            assignments.append([person, next_month, "负责高速相机系统的技术调试和参数优化，完成设备技术验证和性能技术测试。"])
        elif person in ["闫家海", "王健"]:
            assignments.append([person, next_month, "负责测试工装夹具的技术设计和制作，确保工装技术精度和稳定性技术要求。"])
        elif person in ["杨宁", "周少东"]:
            assignments.append([person, next_month, "负责HSV测试质量管理体系技术建设，制定技术标准和验收技术规范。"])
        elif person in ["陈立达", "潘冠军", "郜天威"]:
            assignments.append([person, next_month, "负责HSV系统的技术调试和验证，完成测试环境的技术优化和设备技术维护。"])
        elif person in ["程德", "刘建斌"]:
            assignments.append([person, next_month, "负责HSV测试的技术支持和数据分析，协助完成技术验证和质量控制工作。"])
        
        if len(assignments) >= 7:  # Limit to 6-7 assignments per meeting
            break
    
    return assignments

def create_optimized_meeting(date_str, month_key, filename):
    """Create optimized meeting minutes for a specific month"""
    
    # Create workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = f"{month_key}会议纪要"
    
    # Get standardized styles
    styles = get_standardized_styles()
    
    # Set column widths
    ws.column_dimensions['A'].width = 18
    ws.column_dimensions['B'].width = 55
    ws.column_dimensions['C'].width = 55
    
    current_row = 1
    
    # Title
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '研发例会纪要'
    ws[f'A{current_row}'].font = styles['title_font']
    ws[f'A{current_row}'].fill = styles['title_fill']
    ws[f'A{current_row}'].alignment = styles['center_alignment']
    ws[f'A{current_row}'].border = styles['thin_border']
    ws.row_dimensions[current_row].height = 30
    current_row += 1
    
    # Meeting info
    personnel_count = len(PERSONNEL_DATA.get(month_key, []))
    meeting_info = [
        f'会议日期：{date_str}',
        '项目名称：RD2204-气体发生器排气过程火焰控制研究',
        '项目负责人：石亚伟 (Rocky)',
        f'参会人员：项目核心团队{personnel_count}人全员参加',
        '会议地点：公司研发中心会议室'
    ]
    
    for info in meeting_info:
        ws.merge_cells(f'A{current_row}:C{current_row}')
        ws[f'A{current_row}'] = info
        ws[f'A{current_row}'].font = styles['content_font']
        ws[f'A{current_row}'].alignment = styles['left_alignment']
        ws[f'A{current_row}'].border = styles['thin_border']
        ws.row_dimensions[current_row].height = 20
        current_row += 1
    
    # Empty row
    ws.row_dimensions[current_row].height = 10
    current_row += 1

    # Task section header
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '研发任务安排'
    ws[f'A{current_row}'].font = styles['section_font']
    ws[f'A{current_row}'].fill = styles['section_fill']
    ws[f'A{current_row}'].alignment = styles['center_alignment']
    ws[f'A{current_row}'].border = styles['thin_border']
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Get tasks data
    tasks = get_month_tasks(month_key)

    # Task completed section
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '任务完成情况（上月工作）'
    ws[f'A{current_row}'].font = styles['header_font']
    ws[f'A{current_row}'].fill = styles['header_fill']
    ws[f'A{current_row}'].alignment = styles['center_alignment']
    ws[f'A{current_row}'].border = styles['thin_border']
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Completed tasks
    for task in tasks["completed"]:
        for j, value in enumerate(task, 1):
            cell = ws.cell(row=current_row, column=j)
            cell.value = value
            cell.font = styles['content_font']
            cell.alignment = styles['left_alignment'] if j > 1 else styles['center_alignment']
            cell.border = styles['thin_border']
        ws.row_dimensions[current_row].height = 60
        current_row += 1

    # Empty row
    ws.row_dimensions[current_row].height = 10
    current_row += 1

    # Task planned section
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '任务计划情况（本月工作）'
    ws[f'A{current_row}'].font = styles['header_font']
    ws[f'A{current_row}'].fill = styles['header_fill']
    ws[f'A{current_row}'].alignment = styles['center_alignment']
    ws[f'A{current_row}'].border = styles['thin_border']
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Planned tasks
    for task in tasks["planned"]:
        for j, value in enumerate(task, 1):
            cell = ws.cell(row=current_row, column=j)
            cell.value = value
            cell.font = styles['content_font']
            cell.alignment = styles['left_alignment'] if j > 1 else styles['center_alignment']
            cell.border = styles['thin_border']
        ws.row_dimensions[current_row].height = 60
        current_row += 1

    # Empty row
    ws.row_dimensions[current_row].height = 10
    current_row += 1

    # Personnel section
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '人员分工安排'
    ws[f'A{current_row}'].font = styles['section_font']
    ws[f'A{current_row}'].fill = styles['section_fill']
    ws[f'A{current_row}'].alignment = styles['center_alignment']
    ws[f'A{current_row}'].border = styles['thin_border']
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Personnel headers
    personnel_headers = ['姓名', '研发工作开展日', '工作安排']
    for i, header in enumerate(personnel_headers, 1):
        cell = ws.cell(row=current_row, column=i)
        cell.value = header
        cell.font = styles['header_font']
        cell.fill = styles['header_fill']
        cell.alignment = styles['center_alignment']
        cell.border = styles['thin_border']
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Personnel assignments
    personnel = get_month_personnel(month_key)
    for person in personnel:
        for j, value in enumerate(person, 1):
            cell = ws.cell(row=current_row, column=j)
            cell.value = value
            cell.font = styles['content_font']
            cell.alignment = styles['left_alignment'] if j > 1 else styles['center_alignment']
            cell.border = styles['thin_border']
        ws.row_dimensions[current_row].height = 60
        current_row += 1

    # Save the file
    wb.save(filename)
    print(f"已创建：{filename}")

def main():
    """Main batch optimization function"""
    
    meetings_to_create = [
        ("2022年11月25日", "2022-11", "RD2204会议纪要-2022年11月25日-优化版.xlsx"),
        ("2022年12月28日", "2022-12", "RD2204会议纪要-2022年12月28日-优化版.xlsx"),
        ("2023年1月31日", "2023-01", "RD2204会议纪要-2023年1月31日-优化版.xlsx"),
        ("2023年2月28日", "2023-02", "RD2204会议纪要-2023年2月28日-优化版.xlsx"),
        ("2023年3月24日", "2023-03", "RD2204会议纪要-2023年3月24日-优化版.xlsx"),
        ("2023年4月25日", "2023-04", "RD2204会议纪要-2023年4月25日-优化版.xlsx")
    ]
    
    print("开始批量优化剩余6份会议纪要...")
    
    for date_str, month_key, filename in meetings_to_create:
        try:
            create_optimized_meeting(date_str, month_key, filename)
        except Exception as e:
            print(f"创建 {filename} 时出错：{e}")
    
    print("批量优化完成！")

if __name__ == "__main__":
    main()
