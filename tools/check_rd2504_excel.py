#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RD2504项目Excel文件检查和修复工具
检查会议纪要和进度文件的访问性问题
"""
import pandas as pd
import openpyxl
from openpyxl import load_workbook
import os

def check_excel_files():
    """检查RD2504项目的Excel文件状态"""
    
    print("🔍 检查RD2504项目Excel文件状态")
    print("=" * 50)
    
    base_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2504 15000PSI高压力压力循环测试技术开发 - Rocky/会议纪要"
    
    files_to_check = [
        "RD2504 15000PSI高压力压力循环测试技术开发 - 会议纪要.xlsx",
        "RD2504 15000PSI高压力压力循环测试技术开发 - 进度.xlsx"
    ]
    
    for file_name in files_to_check:
        file_path = os.path.join(base_path, file_name)
        print(f"\n📋 检查文件：{file_name}")
        print(f"路径：{file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print("❌ 文件不存在")
            continue
        
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        print(f"文件大小：{file_size} 字节")
        
        if file_size == 0:
            print("❌ 文件为空")
            continue
        
        # 尝试用openpyxl打开
        try:
            wb = load_workbook(file_path)
            print(f"✅ openpyxl可以打开")
            print(f"工作表：{wb.sheetnames}")
            
            # 检查第一个工作表的内容
            if wb.sheetnames:
                ws = wb.active
                print(f"活动工作表：{ws.title}")
                print(f"最大行数：{ws.max_row}")
                print(f"最大列数：{ws.max_column}")
                
                # 检查前几个单元格
                print("前几个单元格内容：")
                for row in range(1, min(6, ws.max_row + 1)):
                    for col in range(1, min(6, ws.max_column + 1)):
                        cell_value = ws.cell(row, col).value
                        if cell_value:
                            print(f"  {chr(64+col)}{row}: {cell_value}")
            
        except Exception as e:
            print(f"❌ openpyxl打开失败: {e}")
        
        # 尝试用pandas打开
        try:
            df = pd.read_excel(file_path)
            print(f"✅ pandas可以打开")
            print(f"数据形状：{df.shape}")
            if not df.empty:
                print("前几行数据：")
                print(df.head(3))
        except Exception as e:
            print(f"❌ pandas打开失败: {e}")

def main():
    """主函数"""
    check_excel_files()

if __name__ == "__main__":
    main()
