#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Logic Consistency Audit for 4 R&D Projects
Cross-validation between meeting minutes and technical materials
"""
import os
import pandas as pd
from datetime import datetime
import json

class ProjectAuditor:
    def __init__(self, base_path):
        self.base_path = base_path
        self.projects = {}
        self.audit_results = {}
        
    def identify_projects(self):
        """Identify all R&D projects and their structure"""
        
        print("=== 项目结构分析 ===")
        
        # Identify project folders
        project_folders = [
            "RD2201 混合式气体发生器内部工作压力测试方法研究 - Rocky",
            "RD2204 气体发生器排气过程火焰控制研究 - Rocky", 
            "RD2504 15000PSI高压力压力循环测试技术开发 - Rocky",
            "RD2505 高效低耗螺柱摩擦焊焊极技术开发与应用研究 - Rocky"
        ]
        
        for folder in project_folders:
            project_path = os.path.join(self.base_path, folder)
            if os.path.exists(project_path):
                project_code = folder.split()[0]  # Extract RD code
                
                self.projects[project_code] = {
                    "name": folder,
                    "path": project_path,
                    "meeting_minutes_path": os.path.join(project_path, "会议纪要"),
                    "technical_materials_path": os.path.join(project_path, "研发资料"),
                    "meeting_files": [],
                    "technical_files": [],
                    "status": "identified"
                }
                
                print(f"✅ 发现项目：{project_code}")
                print(f"   项目路径：{project_path}")
                
                # Check for meeting minutes
                meeting_path = self.projects[project_code]["meeting_minutes_path"]
                if os.path.exists(meeting_path):
                    meeting_files = [f for f in os.listdir(meeting_path) if f.endswith('.xlsx')]
                    self.projects[project_code]["meeting_files"] = meeting_files
                    print(f"   会议纪要：{len(meeting_files)} 个文件")
                else:
                    print(f"   ⚠️  会议纪要文件夹不存在")
                
                # Check for technical materials
                tech_path = self.projects[project_code]["technical_materials_path"]
                if os.path.exists(tech_path):
                    tech_files = os.listdir(tech_path)
                    self.projects[project_code]["technical_files"] = tech_files
                    print(f"   技术资料：{len(tech_files)} 个文件/文件夹")
                else:
                    # For RD2204, check alternative path
                    if project_code == "RD2204":
                        alt_path = os.path.join(project_path, "RD2204 气体发生器排气过程火焰控制研究")
                        if os.path.exists(alt_path):
                            tech_files = os.listdir(alt_path)
                            self.projects[project_code]["technical_materials_path"] = alt_path
                            self.projects[project_code]["technical_files"] = tech_files
                            print(f"   技术资料：{len(tech_files)} 个文件/文件夹 (替代路径)")
                        else:
                            print(f"   ⚠️  技术资料文件夹不存在")
                    else:
                        print(f"   ⚠️  技术资料文件夹不存在")
                
                print()
        
        return self.projects
    
    def define_audit_strategy(self):
        """Define comprehensive audit strategy"""
        
        print("=== 审核策略制定 ===")
        
        audit_dimensions = {
            "时间线一致性": {
                "description": "验证会议纪要中的时间节点与技术资料中的时间戳是否一致",
                "key_points": [
                    "项目启动时间",
                    "关键里程碑时间",
                    "技术突破时间",
                    "项目完成时间"
                ]
            },
            
            "人员分工匹配": {
                "description": "验证会议纪要中的人员安排与工时清单是否匹配",
                "key_points": [
                    "人员参与时间",
                    "职责分工合理性",
                    "工时分配一致性",
                    "跨项目人员冲突"
                ]
            },
            
            "技术内容连贯性": {
                "description": "验证技术发展脉络的逻辑性和连贯性",
                "key_points": [
                    "技术路线一致性",
                    "技术难点解决过程",
                    "技术成果递进关系",
                    "技术参数一致性"
                ]
            },
            
            "预算使用合理性": {
                "description": "验证预算分配与实际使用的合理性",
                "key_points": [
                    "预算总额一致性",
                    "分期预算合理性",
                    "支出项目匹配性",
                    "成本效益合理性"
                ]
            },
            
            "文档格式规范性": {
                "description": "验证文档格式和内容的规范性",
                "key_points": [
                    "命名规范一致性",
                    "版本控制规范性",
                    "内容完整性",
                    "格式标准化"
                ]
            }
        }
        
        for dimension, details in audit_dimensions.items():
            print(f"📋 {dimension}：{details['description']}")
            for point in details['key_points']:
                print(f"   • {point}")
            print()
        
        return audit_dimensions
    
    def audit_project(self, project_code):
        """Audit a specific project"""
        
        print(f"=== 审核项目：{project_code} ===")
        
        if project_code not in self.projects:
            print(f"❌ 项目 {project_code} 不存在")
            return None
        
        project = self.projects[project_code]
        audit_result = {
            "project_code": project_code,
            "project_name": project["name"],
            "issues": [],
            "recommendations": [],
            "overall_score": 0
        }
        
        # 1. Check meeting minutes availability
        if not project["meeting_files"]:
            audit_result["issues"].append({
                "category": "文档完整性",
                "severity": "高",
                "description": "缺少会议纪要文件",
                "impact": "无法验证项目进展和人员参与情况"
            })
        else:
            print(f"✅ 会议纪要文件：{len(project['meeting_files'])} 个")
            
        # 2. Check technical materials availability  
        if not project["technical_files"]:
            audit_result["issues"].append({
                "category": "文档完整性", 
                "severity": "高",
                "description": "缺少技术资料文件",
                "impact": "无法验证技术发展过程和成果"
            })
        else:
            print(f"✅ 技术资料文件：{len(project['technical_files'])} 个")
        
        # 3. Analyze meeting minutes content (if available)
        if project["meeting_files"]:
            self.analyze_meeting_minutes(project_code, audit_result)
        
        # 4. Analyze technical materials (if available)
        if project["technical_files"]:
            self.analyze_technical_materials(project_code, audit_result)
        
        # 5. Cross-validation
        self.cross_validate_project(project_code, audit_result)
        
        self.audit_results[project_code] = audit_result
        return audit_result
    
    def analyze_meeting_minutes(self, project_code, audit_result):
        """Analyze meeting minutes for consistency"""
        
        print(f"📋 分析会议纪要...")
        
        project = self.projects[project_code]
        meeting_path = project["meeting_minutes_path"]
        
        # Check for different versions of meeting minutes
        meeting_files = project["meeting_files"]
        
        # Look for different versions (original, optimized, template, enriched)
        versions = {
            "original": [],
            "optimized": [],
            "template": [],
            "enriched": []
        }
        
        for file in meeting_files:
            if "优化版" in file:
                versions["optimized"].append(file)
            elif "新模板版" in file:
                versions["template"].append(file)
            elif "丰富版" in file:
                versions["enriched"].append(file)
            else:
                versions["original"].append(file)
        
        # Report version analysis
        for version_type, files in versions.items():
            if files:
                print(f"   {version_type}: {len(files)} 个文件")
        
        # Check for version consistency
        if len(versions["original"]) > 0 and len(versions["optimized"]) > 0:
            if len(versions["original"]) != len(versions["optimized"]):
                audit_result["issues"].append({
                    "category": "版本一致性",
                    "severity": "中",
                    "description": f"原版会议纪要({len(versions['original'])})与优化版({len(versions['optimized'])})数量不匹配",
                    "impact": "可能存在版本管理问题"
                })
        
        return versions
    
    def analyze_technical_materials(self, project_code, audit_result):
        """Analyze technical materials for consistency"""
        
        print(f"🔬 分析技术资料...")
        
        project = self.projects[project_code]
        tech_path = project["technical_materials_path"]
        
        # Analyze folder structure
        tech_files = project["technical_files"]
        
        # Check for logical folder structure
        expected_phases = ["1.", "2.", "3.", "4.", "5."]  # Project phases
        found_phases = []
        
        for item in tech_files:
            for phase in expected_phases:
                if item.startswith(phase):
                    found_phases.append(phase)
                    break
        
        print(f"   发现项目阶段：{len(set(found_phases))} 个")
        
        # Check for missing phases
        missing_phases = set(expected_phases) - set(found_phases)
        if missing_phases:
            audit_result["issues"].append({
                "category": "技术资料完整性",
                "severity": "中", 
                "description": f"缺少项目阶段资料：{', '.join(missing_phases)}",
                "impact": "技术发展过程可能不完整"
            })
        
        return found_phases
    
    def cross_validate_project(self, project_code, audit_result):
        """Cross-validate between meeting minutes and technical materials"""
        
        print(f"🔄 交叉验证...")
        
        # This is where we would implement detailed cross-validation
        # For now, we'll do basic checks
        
        project = self.projects[project_code]
        
        # Check if both meeting minutes and technical materials exist
        has_meetings = len(project["meeting_files"]) > 0
        has_tech = len(project["technical_files"]) > 0
        
        if has_meetings and has_tech:
            print("   ✅ 具备交叉验证条件")
            audit_result["overall_score"] += 30
        elif has_meetings or has_tech:
            print("   ⚠️  仅有部分资料，交叉验证受限")
            audit_result["overall_score"] += 15
            audit_result["issues"].append({
                "category": "资料完整性",
                "severity": "高",
                "description": "缺少会议纪要或技术资料，无法进行完整交叉验证",
                "impact": "审核可信度降低"
            })
        else:
            print("   ❌ 缺少基本资料，无法交叉验证")
            audit_result["issues"].append({
                "category": "资料完整性",
                "severity": "严重",
                "description": "同时缺少会议纪要和技术资料",
                "impact": "项目真实性存疑"
            })
    
    def generate_audit_report(self):
        """Generate comprehensive audit report"""
        
        print("\n" + "="*60)
        print("📊 综合审核报告")
        print("="*60)
        
        total_projects = len(self.projects)
        audited_projects = len(self.audit_results)
        
        print(f"审核范围：{total_projects} 个项目")
        print(f"已审核：{audited_projects} 个项目")
        print()
        
        # Summary by project
        for project_code, result in self.audit_results.items():
            print(f"🔍 项目：{project_code}")
            print(f"   项目名称：{result['project_name']}")
            print(f"   发现问题：{len(result['issues'])} 个")
            
            # Categorize issues by severity
            severity_count = {"严重": 0, "高": 0, "中": 0, "低": 0}
            for issue in result["issues"]:
                severity = issue.get("severity", "低")
                severity_count[severity] += 1
            
            for severity, count in severity_count.items():
                if count > 0:
                    print(f"   {severity}级问题：{count} 个")
            
            print(f"   综合评分：{result['overall_score']}/100")
            print()
        
        # Overall recommendations
        print("📋 总体建议：")
        print("1. 确保所有项目都有完整的会议纪要和技术资料")
        print("2. 建立统一的文档版本管理规范")
        print("3. 加强项目间人员分工的协调管理")
        print("4. 完善技术发展过程的文档记录")
        print("5. 建立定期的内部审核机制")
        
        return self.audit_results

def main():
    """Main audit execution"""
    
    base_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核"
    
    auditor = ProjectAuditor(base_path)
    
    # Step 1: Identify projects
    projects = auditor.identify_projects()
    
    # Step 2: Define audit strategy
    audit_strategy = auditor.define_audit_strategy()
    
    # Step 3: Audit each project
    for project_code in projects.keys():
        auditor.audit_project(project_code)
        print()
    
    # Step 4: Generate comprehensive report
    audit_results = auditor.generate_audit_report()
    
    return audit_results

if __name__ == "__main__":
    main()
