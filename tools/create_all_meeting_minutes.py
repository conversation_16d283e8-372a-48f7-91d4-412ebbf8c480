#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create all monthly meeting minutes for RD2204 project
"""
import pandas as pd
from datetime import datetime

def create_january_meeting():
    """Create January 2023 meeting minutes"""

    with pd.ExcelWriter('RD2204会议纪要-2023年1月31日.xlsx', engine='openpyxl') as writer:
        
        # Meeting header data
        header_data = [
            ["研发例会", "", ""],
            ["会议日期：2023年1月31日", "", ""],
            ["项目名称：RD2204-气体发生器排气过程火焰控制研究", "", ""],
            ["项目负责人：石亚伟 (Rocky)", "", ""],
            ["参会人员：项目核心团队14人全员参加", "", ""],
            ["会议地点：公司研发中心会议室", "", ""],
            ["", "", ""],
            ["研发任务安排", "", ""],
            ["序号", "任务完成情况", "任务计划情况"]
        ]
        
        # Task data
        task_data = [
            [1, "成功完成了HSV软件的最终设置与验证工作（1月31日完成）。经过一个月的深度调试，确定了帧率>1500fps、曝光时间666μs的最优参数配置。", "启动工艺问题的深入调查与研究工作，重点优化样品固定方式和测试流程标准化。"],
            [2, "突破了HSV软件设置的关键技术难点。通过与供应商的深度技术交流和大量的参数测试，解决了软件与硬件匹配的复杂问题。", "完成HSV柱状工装的制作和验证，确保工装精度满足不同型号气体发生器的测试需求。"],
            [3, "完成了HSV系统的全面集成和初步验证。整个系统运行稳定，各项技术指标达到设计要求，为后续测试奠定了坚实基础。", "开展小批量试运行验证工作，通过实际测试验证HSV系统的稳定性和可靠性。"],
            [4, "建立了HSV测试的标准化操作流程。制定了详细的测试步骤、参数设置、数据记录等操作规范，确保测试结果的一致性。", "深化工艺问题研究，优化测试环境控制和样品处理流程，提高测试精度和效率。"],
            [5, "完成了项目中期技术评估和风险分析。项目技术路线得到验证，关键技术难点已突破，为项目成功奠定了基础。", "建立完善的质量管理体系和文档控制流程，为项目验收做好准备。"],
            ["", "", ""],
            ["", "", ""],
            ["人员分工安排", "", ""],
            ["姓名", "研发工作开展日", "工作安排"]
        ]
        
        # Personnel assignment data
        personnel_data = [
            ["石亚伟", "1月", "全面负责并主导HSV软件的最优参数配置调试工作，解决软件设置中的关键技术难点。"],
            ["陈立达", "1月", "负责执行不同距离、位置、参数下的软件调试实验，配合完成软件参数的优化验证。"],
            ["潘冠军", "1月", "协助进行软件设置工作，负责与供应商的技术沟通确认和操作培训。"],
            ["朱丰海", "1月", "负责HSV测试工艺的深化研究，制定详细的测试步骤和质量控制要点。"],
            ["杨宁、周少东", "1月", "负责HSV测试质量管理体系的建立，制定设备维护和数据管理规范。"],
            ["郝天威", "1月", "负责测试环境的维护和优化，确保HSV测试条件的稳定性。"],
            ["杨秀秀", "1月", "负责项目进度跟踪和文档管理，建立完善的项目资料归档体系。"],
            ["", "", ""],
            ["设备与物料配置进展", "", ""],
            ["类别", "本月配置内容", "用途说明"]
        ]
        
        # Equipment and materials data
        equipment_data = [
            ["HSV系统集成", "完成高速相机、光源、背景板的精确安装和光路调试，实现了标准化的拍摄环境配置", "建立稳定可靠的HSV火焰捕捉平台"],
            ["测试工装完善", "完成所有测试工装夹具的安装调试，包括样品固定支架、定位系统等关键部件的精度验证", "确保测试样品的精确定位和重现性"],
            ["环境控制优化", "完成实验室环境的最终优化，包括照明系统、温湿度控制、防尘措施等环境因素的标准化", "消除环境因素对HSV测试结果的影响"],
            ["软件配置准备", "完成HSV软件的基础安装和初步配置，建立软件操作的基础环境", "为深度软件调试做好准备"],
            ["质量控制体系", "建立HSV测试的质量控制流程，包括设备检查、环境监控、数据验证等质量保证措施", "确保HSV测试结果的可靠性和一致性"],
            ["标准化物料", "完成测试耗材、工具、备件的标准化配置和库存管理，建立物料使用和补充的规范流程", "保证HSV测试工作的连续性"],
            ["", "", ""],
            ["", "", ""]
        ]
        
        # Meeting summary
        summary_data = [
            ["会议摘要", "", ""],
            ["本次会议是2022年度的最后一次项目例会，也是项目从硬件建设向软件调试转换的关键节点会议。会议全面回顾了12月份的工作进展，确认了HSV测试台架搭建和测试背景调试的完成情况。项目团队在硬件集成方面取得了重要突破，为后续的软件调试奠定了坚实基础。", "", ""],
            ["", "", ""],
            ["会议重点分析了HSV软件设置工作的进展情况。虽然软件设置工作比原计划出现了约两周的延迟，但项目团队通过深入的技术分析，找到了延迟的根本原因。HSV软件的参数设置涉及帧率、曝光时间、触发方式等多个复杂参数，需要与硬件系统进行精确匹配，这比预期更加复杂和耗时。", "", ""],
            ["", "", ""],
            ["Rocky在会上强调，HSV软件是整个项目的核心，是项目成功的关键所在。软件设置的质量直接决定了火焰捕捉的效果和数据分析的准确性。因此，宁可在时间上有所延迟，也要确保软件调试到最佳状态。项目团队要以质量为第一要务，不能因为进度压力而降低技术标准。", "", ""],
            ["", "", ""],
            ["会议确定了2023年1月份的工作重点：一是集中力量解决HSV软件设置问题，确保软件参数达到最优配置；二是开展工艺问题的深入研究，优化测试流程和操作规范；三是准备试运行验证工作，为全面测试做好准备。会议要求项目团队要保持攻坚克难的精神，确保项目在新的一年里取得突破性进展。", "", ""],
            ["", "", ""],
            ["会议还对项目的风险管控进行了专门讨论。针对软件调试的复杂性，制定了详细的风险预案和应对措施。同时要求加强与供应商的技术交流，充分利用外部技术资源，确保项目按计划推进。", "", ""]
        ]
        
        # Combine all data
        all_data = header_data + task_data + personnel_data + equipment_data + summary_data
        
        # Create DataFrame
        df = pd.DataFrame(all_data)
        
        # Write to Excel
        df.to_excel(writer, sheet_name='2022年12月会议纪要', index=False, header=False)
        
        # Get workbook and worksheet for formatting
        workbook = writer.book
        worksheet = writer.sheets['2022年12月会议纪要']
        
        # Set column widths
        worksheet.column_dimensions['A'].width = 15
        worksheet.column_dimensions['B'].width = 50
        worksheet.column_dimensions['C'].width = 50
        
        print("Excel文件已创建：RD2204会议纪要-2022年12月28日.xlsx")

if __name__ == "__main__":
    create_december_meeting()
