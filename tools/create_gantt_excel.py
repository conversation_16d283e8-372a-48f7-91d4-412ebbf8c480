#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Excel file with Gantt chart and issue list for RD2204 project
"""
import pandas as pd
from datetime import datetime, date

def create_gantt_data():
    """Create Gantt chart data"""
    gantt_data = [
        # 第一阶段：项目启动与调研（2022年9月-2022年10月）
        ["1", "第一阶段：项目启动与调研", "", "2022-09-01", "2022-10-15", 45, "", "已完成"],
        ["1.1", "项目启动会议", "石亚伟", "2022-09-01", "2022-09-02", 2, "", "已完成"],
        ["1.2", "技术与测试方法调研", "卢卫中、朱丰海", "2022-09-03", "2022-09-30", 28, "1.1", "已完成"],
        ["1.3", "概念讨论和技术标准制定", "卢卫中、朱丰海", "2022-10-01", "2022-10-15", 15, "1.2", "已完成"],
        
        # 第二阶段：方案设计与硬件选型（2022年10月-2022年11月）
        ["2", "第二阶段：方案设计与硬件选型", "", "2022-10-16", "2022-11-30", 46, "", "已完成"],
        ["2.1", "安全风险评估", "杨宁、周少东", "2022-10-16", "2022-10-25", 10, "1.3", "已完成"],
        ["2.2", "技术方案和操作步骤设计", "卢卫中、朱丰海", "2022-10-26", "2022-11-05", 11, "2.1", "已完成"],
        ["2.3", "关键硬件调研与高速相机选型确认", "赵辉、石亚伟", "2022-11-06", "2022-11-20", 15, "2.2", "已完成"],
        ["2.4", "标准设置和规范制定", "周少东", "2022-11-21", "2022-11-30", 10, "2.3", "已完成"],
        
        # 第三阶段：测试实施与调试（2022年12月-2023年1月）
        ["3", "第三阶段：测试实施与调试", "", "2022-12-01", "2023-01-31", 62, "", "已完成"],
        ["3.1", "测试工装夹具设计与制作", "闫家海、王健", "2022-12-01", "2022-12-15", 15, "2.4", "已完成"],
        ["3.2", "测试台架搭建", "王健、潘冠军", "2022-12-16", "2022-12-30", 15, "3.1", "已完成"],
        ["3.3", "测试背景调试", "陈立达、郝天威", "2022-12-16", "2022-12-30", 15, "3.1", "已完成"],
        ["3.4", "HSV软件设置与调试", "石亚伟(主导)、陈立达、潘冠军", "2022-12-27", "2023-01-31", 36, "3.2,3.3", "已完成"],
        
        # 第四阶段：工艺验证与优化（2023年2月-2023年3月）
        ["4", "第四阶段：工艺验证与优化", "", "2023-02-01", "2023-03-31", 59, "", "已完成"],
        ["4.1", "工艺问题调查与研究", "朱丰海、杨宁", "2023-02-01", "2023-02-28", 28, "3.4", "已完成"],
        ["4.2", "试运行验证", "陈立达、潘冠军(执行)、石亚伟(监督)", "2023-03-01", "2023-03-21", 21, "4.1", "已完成"],
        ["4.3", "工艺参数固化", "朱丰海", "2023-03-15", "2023-03-25", 11, "4.2", "已完成"],
        ["4.4", "工艺测试规范编制", "朱丰海、周少东", "2023-03-22", "2023-03-31", 10, "4.3", "已完成"],
        
        # 第五阶段：项目验收（2023年4月）
        ["5", "第五阶段：项目验收", "", "2023-04-01", "2023-04-25", 25, "", "已完成"],
        ["5.1", "HSV作业指导书编制", "朱丰海、周少东", "2023-04-01", "2023-04-15", 15, "4.4", "已完成"],
        ["5.2", "项目验收资料整理", "杨秀秀、石亚伟", "2023-04-16", "2023-04-20", 5, "5.1", "已完成"],
        ["5.3", "项目技术验收", "石亚伟", "2023-04-25", "2023-04-25", 1, "5.2", "已完成"]
    ]
    
    columns = ["任务编号", "任务名称", "负责人", "开始时间", "结束时间", "工期(天)", "前置任务", "状态"]
    return pd.DataFrame(gantt_data, columns=columns)

def create_milestone_data():
    """Create milestone data"""
    milestone_data = [
        ["M1", "2022-10-28", "技术方案评审通过", "对应实际会议日期"],
        ["M2", "2022-12-16", "测试背景调试完成", "对应实际会议记录"],
        ["M3", "2023-01-31", "HSV软件设置完成", "关键技术突破"],
        ["M4", "2023-03-21", "试运行验证完成", "对应实际会议记录"],
        ["M5", "2023-04-25", "项目技术验收", "项目正式完成"]
    ]
    
    columns = ["里程碑", "时间", "标志性成果", "备注"]
    return pd.DataFrame(milestone_data, columns=columns)

def create_issue_data():
    """Create issue list data with standard format"""
    issue_data = [
        # 技术问题 - 高优先级
        [1, "高速相机参数优化", "通过多轮DOE测试，最终固化标准：帧率>1500fps，曝光时间666μs", "赵辉、潘冠军", "2022-12-31", "2023-01-31", "已完成"],
        [2, "拍摄距离标准化", "经测试验证，确定相机距发生器5英尺，距有机玻璃背景板2英尺", "陈立达、闫家海", "2022-12-31", "2022-12-30", "已完成"],
        [3, "光源配置优化", "确定光源与相机呈直线布局，灯头加装柔光罩，确保均匀照明无过曝", "王健、潘冠军", "2022-12-31", "2022-12-30", "已完成"],
        [4, "HSV软件设置延迟问题", "原计划12.13完成，实际12.27完成，比计划延迟约两周", "石亚伟(主导)、陈立达", "2022-12-13", "2022-12-27", "已完成"],
        
        # 技术问题 - 中优先级
        [5, "测试样品固定方案", "设计并制作了可适配公司全系列产品的多功能工装夹具", "闫家海、王健", "2022-12-15", "2022-12-15", "已完成"],
        [6, "背景干扰消除", "最终选用18%标准中性灰背景板，并优化拍摄环境为暗室", "陈立达", "2022-12-30", "2022-12-16", "已完成"],
        [7, "数据采集同步", "优化硬件连接，采用点爆控制器的高电平信号直接触发相机录制", "潘冠军、陈立达", "2023-01-20", "2023-01-20", "已完成"],
        [8, "试运行验证", "通过小批量试运行验证并确定关键测试步骤", "陈立达、潘冠军", "2023-03-21", "2023-03-21", "已完成"],
        
        # 技术问题 - 低优先级
        [9, "软件兼容性", "将相机控制软件PCC升级至最新版本，并优化了电脑工作站的配置", "赵辉、潘冠军", "2023-02-20", "2023-02-20", "已完成"],
        
        # 管理与标准化问题
        [10, "操作培训标准化", "编制并发布了《NEL-WIT-014 HSV作业指导书》，并对所有相关测试员进行了培训与考核", "朱丰海、石亚伟", "2023-04-15", "2023-04-15", "已完成"],
        [11, "质量控制标准", "建立了包含图像清晰度、数据完整性的质量控制流程和验收标准", "周少东、杨秀秀", "2023-03-31", "2023-03-31", "已完成"],
        [12, "文档管理规范", "建立了包含视频原始数据、分析报告和WI文件的标准化文档管理体系", "杨秀秀", "2023-04-20", "2023-04-20", "已完成"],
        
        # 风险问题
        [13, "设备安全风险", "制定了包含设备接地、电气安全检查的安全操作规程和应急预案", "杨宁、周少东", "2022-10-25", "2022-10-25", "已完成"],
        [14, "测试安全风险", "建立了包含人员防护（护目镜）、点爆前清场的安全防护措施和监控机制", "杨宁、石亚伟", "2022-10-25", "2022-10-25", "已完成"]
    ]
    
    columns = ["S/N", "问题描述", "改善措施", "责任人", "计划完成时间", "实际完成时间", "最新进度"]
    return pd.DataFrame(issue_data, columns=columns)

def main():
    # Create Excel writer with simple formatting
    with pd.ExcelWriter('RD2204项目甘特图和问题清单-修正版.xlsx', engine='openpyxl') as writer:

        # Create Gantt chart
        gantt_df = create_gantt_data()
        gantt_df.to_excel(writer, sheet_name='甘特图', index=False)

        # Create milestones
        milestone_df = create_milestone_data()
        milestone_df.to_excel(writer, sheet_name='里程碑', index=False)

        # Create issue list
        issue_df = create_issue_data()
        issue_df.to_excel(writer, sheet_name='问题清单', index=False)

if __name__ == "__main__":
    main()
    print("Excel文件已创建：RD2204项目甘特图和问题清单-修正版.xlsx")
