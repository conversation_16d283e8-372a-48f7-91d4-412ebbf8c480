#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Excel file for RD2204 meeting minutes using the template structure
"""
import pandas as pd
from datetime import datetime

def create_meeting_minutes_excel():
    """Create Excel file with meeting minutes for September 2022"""

    # Create Excel writer
    with pd.ExcelWriter('RD2204会议纪要-2022年9月30日.xlsx', engine='openpyxl') as writer:
        
        # Meeting header data
        header_data = [
            ["研发例会", "", ""],
            ["会议日期：2022年9月30日", "", ""],
            ["项目名称：RD2204-气体发生器排气过程火焰控制研究", "", ""],
            ["项目负责人：石亚伟 (Rocky)", "", ""],
            ["参会人员：项目核心团队14人全员参加", "", ""],
            ["会议地点：公司研发中心会议室", "", ""],
            ["", "", ""],
            ["研发任务安排", "", ""],
            ["序号", "任务完成情况", "任务计划情况"]
        ]
        
        # Task data
        task_data = [
            [1, "完成了项目正式启动与核心团队组建工作。经过充分讨论，确定了由实验室经理、技术研发工程师、工艺工程师、质量工程师、测试员和实验员组成的14人研发团队，明确了各成员在项目中的核心职责分工和协作机制。", "启动HSV测试台架的详细方案设计与硬件选型工作。重点完成光学系统布局设计、相机与光源的技术参数确认，以及测试环境的初步规划。"],
            [2, "完成了技术方案的内部评审工作，明确了基于高速摄像技术的火焰形态捕捉技术路线。通过对比分析多种技术方案，确定了采用帧率>1500fps的高速相机配合1000W可调光束光源的技术架构。", "完成核心设备的技术规格确认与采购流程启动。包括高速相机Phantom VEO 710S的技术参数验证、1000W光源系统的配套要求确认，以及相关配套设备的技术对接工作。"],
            [3, "完成了项目安全风险评估工作，制定了涵盖设备安全、测试安全、人员防护等方面的项目安全管理规范。特别针对气体发生器测试的特殊性，建立了完善的安全操作流程和应急预案。", "启动测试工装夹具的设计工作。包括气体发生器样品固定系统的机械设计、高速相机拍摄环境的配置优化，以及测试过程中样品定位精度的保证措施。"],
            [4, "建立了实验室基础设施的全面改进计划，涵盖环境控制系统优化、安全防护设施完善、测试区域布局调整等多个方面。同时完成了项目所需基础工具和标准化耗材的需求分析。", "完成HSV测试标准和操作规范的初步框架制定。建立包含测试流程、质量控制、数据记录、结果分析等环节的标准化体系，为后续测试工作提供规范指导。"],
            [5, "完成了项目预算的详细分解和资源配置计划，确保各阶段工作的资源保障。建立了项目进度跟踪机制和风险控制措施，为项目顺利推进奠定了管理基础。", "建立项目质量管理体系和文档管理规范。制定详细的项目文档编制标准、版本控制流程，以及知识管理和经验积累机制。"],
            ["", "", ""],
            ["", "", ""],
            ["人员分工安排", "", ""],
            ["姓名", "研发工作开展日", "工作安排"]
        ]
        
        # Personnel assignment data
        personnel_data = [
            ["卢卫中", "10月", "负责HSV光学测试台架的总体方案设计与理论分析工作。包括光学系统布局优化、相机与光源的相对位置关系确定、拍摄角度和距离的理论计算，以及整体测试台架的技术参数要求制定。"],
            ["朱丰海", "10月", "负责测试方法的工艺流程设计工作。深入拆解HSV测试的详细步骤，明确各环节的关键控制点，制定标准化的操作流程，并建立测试质量控制和数据记录规范。"],
            ["赵辉", "10月", "负责高速相机和光源系统的技术调研、选型确认及供应商技术对接工作。重点完成Phantom VEO 710S高速相机的技术参数验证、1000W光源系统的配套要求确认，以及相关配套设备的技术规格对接。"],
            ["闫家海、王健", "10月", "负责测试工装夹具的机械设计工作。包括气体发生器样品固定支架的精密设计、定位系统的精度保证、工装夹具的标准化设计，以及与测试台架的接口匹配设计。"],
            ["杨宁、周少东", "10月", "负责完善安全操作规程和建立HSV测试的质量控制标准。制定涵盖设备操作、人员防护、应急处理的安全管理体系，以及测试过程的质量控制流程和验收标准。"],
            ["陈立达、潘冠军、郝天威", "10月", "负责实验室环境优化和基础设施配套工作。包括测试区域的环境控制、设备安装调试的现场准备、以及测试过程中的技术支持和设备维护工作。"],
            ["杨秀秀", "10月", "负责项目文档管理和质量监控工作。建立项目文档编制标准、版本控制流程，以及项目进度跟踪和质量监控机制，确保项目各阶段工作的规范性和可追溯性。"],
            ["", "", ""],
            ["设备与物料配置", "", ""],
            ["类别", "配置内容", "用途说明"]
        ]
        
        # Equipment and materials data
        equipment_data = [
            ["核心设备", "高速相机Phantom VEO 710S（帧率>1500fps）、1000W可调光束光源、光纤HDMI线（2.1版18K，15米）、专业显示器支架", "HSV火焰捕捉的核心设备系统，实现高精度火焰形态记录与分析"],
            ["环境配套", "实验室LED照明系统优化、双层温度箱调试维护、除湿机定制过滤网、窗帘遮光系统、温湿度监控设备", "确保HSV测试环境的稳定性和一致性，消除环境因素对测试结果的影响"],
            ["安全防护", "防护服装、护目镜、安全门系统、烟感报警器、应急处理设备、安全操作标识", "保障气体发生器测试过程中的人员安全和设备安全，建立完善的安全防护体系"],
            ["测试工装", "样品固定支架、定位座、夹爪系统、工装夹具、精密定位装置", "确保气体发生器在测试过程中的精确定位和稳定固定，保证测试结果的重现性"],
            ["基础工具", "测试工具套装、连接线束、标准化耗材、无尘布、密封件、接头配件", "支持HSV系统搭建、调试和日常维护工作，确保测试过程的连续性"],
            ["数据处理", "移动硬盘（2TB）、数据线缆、备份存储设备", "支持HSV测试数据的采集、存储和备份，确保测试数据的安全性和完整性"],
            ["", "", ""],
            ["", "", ""]
        ]
        
        # Meeting summary
        summary_data = [
            ["会议摘要", "", ""],
            ["本次会议标志着RD2204项目正式启动。会议确认了项目的核心目标：通过高速摄像技术实现气体发生器排气过程火焰形态的精确捕捉与分析，为气体发生器控制策略优化提供科学依据。项目将建立一套标准化的、基于高速相机的火焰形态捕捉与分析方法，实现对气体发生器及安全气囊的通用化测试能力。", "", ""],
            ["", "", ""],
            ["会议明确了技术攻关与基础建设并行的发展策略。技术攻关方面，要快速推进HSV核心技术的方案设计和设备选型，重点突破高速摄像参数优化、拍摄环境配置、数据采集同步等关键技术难点。基础建设方面，要同步完善实验室基础设施，建立标准化的测试环境和安全管理体系，确保项目具备可持续发展的硬件基础。", "", ""],
            ["", "", ""],
            ["Rocky在会上强调，本项目的成功关键在于建立一套可重复、可推广、具有高抗干扰性的HSV测试能力。项目必须从初期就注重标准化建设，不仅要实现技术突破，更要形成完整的方法论和操作规范，为公司在气体发生器测试领域建立核心竞争优势。项目团队要始终坚持质量第一、安全至上的原则，确保每个环节都经得起验证和推敲。", "", ""],
            ["", "", ""],
            ["会议最终确定了项目的阶段性推进计划。10月份的核心任务是完成技术方案的详细设计和核心设备的采购确认，建立完善的项目管理体系和质量控制机制。11月份重点进行设备到货验收和测试台架搭建，同时启动工装夹具的制作和实验室环境的优化改造。12月份将进入系统集成和调试阶段，为后续的全面测试验证奠定坚实基础。", "", ""],
            ["", "", ""],
            ["会议还特别强调了项目的创新性和挑战性。HSV技术在气体发生器测试领域的应用尚属首次尝试，项目团队要做好充分的技术储备和风险预案。同时要加强与外部专家和供应商的技术交流，及时获取最新的技术信息和解决方案，确保项目始终处于技术前沿。", "", ""]
        ]
        
        # Combine all data
        all_data = header_data + task_data + personnel_data + equipment_data + summary_data
        
        # Create DataFrame
        df = pd.DataFrame(all_data)
        
        # Write to Excel
        df.to_excel(writer, sheet_name='2022年9月会议纪要', index=False, header=False)

        # Get workbook and worksheet for formatting
        workbook = writer.book
        worksheet = writer.sheets['2022年9月会议纪要']
        
        # Set column widths
        worksheet.column_dimensions['A'].width = 15
        worksheet.column_dimensions['B'].width = 50
        worksheet.column_dimensions['C'].width = 50
        
        print("Excel文件已创建：RD2204会议纪要-2022年9月30日.xlsx")

if __name__ == "__main__":
    create_meeting_minutes_excel()
