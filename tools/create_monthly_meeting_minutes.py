#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create monthly meeting minutes for RD2204 project
"""
import pandas as pd
from datetime import datetime

def create_november_meeting():
    """Create November 2022 meeting minutes"""

    with pd.ExcelWriter('RD2204会议纪要-2022年11月25日.xlsx', engine='openpyxl') as writer:
        
        # Meeting header data
        header_data = [
            ["研发例会", "", ""],
            ["会议日期：2022年11月25日", "", ""],
            ["项目名称：RD2204-气体发生器排气过程火焰控制研究", "", ""],
            ["项目负责人：石亚伟 (Rocky)", "", ""],
            ["参会人员：项目核心团队14人全员参加", "", ""],
            ["会议地点：公司研发中心会议室", "", ""],
            ["", "", ""],
            ["研发任务安排", "", ""],
            ["序号", "任务完成情况", "任务计划情况"]
        ]
        
        # Task data
        task_data = [
            [1, "完成了核心设备的采购和到货验收工作。Phantom VEO 710S高速相机和1000W光源系统已到货，完成了设备的初步检验和技术参数确认。", "启动HSV测试台架的搭建工作，完成相机、光源、背景板的精确定位和安装调试。"],
            [2, "完成了测试工装夹具的制作和验证工作。包括样品固定支架、定位座、夹爪系统等关键部件已完成加工，并通过了精度测试。", "完成实验室环境改造的主体工程，包括照明系统、温湿度控制、安全防护等设施的安装调试。"],
            [3, "建立了完善的HSV测试标准和操作规范框架。制定了详细的测试流程、质量控制要点和数据记录要求，形成了标准化的操作指导。", "启动HSV系统的初步集成工作，完成硬件连接和基础软件配置，为后续调试做好准备。"],
            [4, "完成了项目物料的标准化配置和库存管理。建立了包含测试工具、耗材、备件在内的完整物料体系，确保项目执行的连续性。", "建立HSV测试的质量管理体系，制定设备维护、数据管理、文档控制等管理规范。"],
            [5, "完成了安全管理体系的实施和验证。所有安全设施已安装到位，安全操作规程已培训到人，应急预案已演练验证。", "完成项目中期评估和风险分析，制定后续阶段的详细实施计划和风险控制措施。"],
            ["", "", ""],
            ["", "", ""],
            ["人员分工安排", "", ""],
            ["姓名", "研发工作开展日", "工作安排"]
        ]
        
        # Personnel assignment data
        personnel_data = [
            ["卢卫中", "12月", "负责HSV测试台架的精确安装和光学系统调试，确保相机、光源、背景板的精确定位和光路优化。"],
            ["朱丰海", "12月", "负责HSV测试工艺的深化完善，制定详细的测试步骤和参数设置指导，建立标准化的操作流程。"],
            ["赵辉", "12月", "负责高速相机系统的技术调试和参数优化，协调供应商提供技术支持和操作培训。"],
            ["闫家海、王健", "12月", "负责测试工装夹具的安装调试和精度验证，确保样品固定系统的稳定性和重现性。"],
            ["杨宁、周少东", "12月", "负责HSV测试质量控制体系的建立和实施，制定设备维护和数据管理规范。"],
            ["陈立达、潘冠军、郝天威", "12月", "负责实验室环境的最终优化和HSV系统的基础调试，为软件设置阶段做好硬件准备。"],
            ["杨秀秀", "12月", "负责项目中期评估报告的编制和项目资料的整理归档，建立完善的文档管理体系。"],
            ["", "", ""],
            ["设备与物料配置进展", "", ""],
            ["类别", "本月配置内容", "用途说明"]
        ]
        
        # Equipment and materials data
        equipment_data = [
            ["核心设备验收", "完成Phantom VEO 710S高速相机和1000W光源系统的到货验收，设备技术参数符合要求，配套数据线缆和控制软件已安装", "建立完整的HSV火焰捕捉硬件平台"],
            ["实验室改造完成", "完成LED照明系统优化、双层温度箱维护、除湿机过滤网更换、窗帘遮光系统安装，实验室环境达到HSV测试要求", "确保HSV测试环境的稳定性和一致性"],
            ["安全防护到位", "完成防护服装、护目镜配发，安全门系统和烟感报警器安装调试，应急处理设备配置到位", "建立完善的安全防护体系"],
            ["工装制作完成", "完成样品固定支架、定位座、夹爪系统的制作和精度验证，工装夹具满足测试精度要求", "确保测试样品的精确定位和稳定固定"],
            ["基础工具配套", "完成测试工具、连接线束、标准化耗材的采购配置，建立无尘布、密封件、接头配件等消耗品管理体系", "支持HSV系统搭建和日常维护"],
            ["数据管理完善", "配置移动硬盘、数据线缆等存储设备，建立数据采集、存储、备份的完整管理流程", "确保测试数据的安全性和完整性"],
            ["计量设备校准", "完成机装泄露瓶、真空计等关键测量设备的外部校准，确保测试数据的准确性和可追溯性", "保证HSV测试结果的可靠性"],
            ["", "", ""]
        ]
        
        # Meeting summary
        summary_data = [
            ["会议摘要", "", ""],
            ["本次会议是项目实施阶段的重要里程碑会议，标志着RD2204项目硬件建设阶段的圆满完成。会议对11月份的各项工作进行了全面总结，确认了核心设备采购、工装制作、环境改造等关键任务的完成情况。项目团队在短短一个月内完成了大量的基础建设工作，为后续的系统集成和调试奠定了坚实基础。", "", ""],
            ["", "", ""],
            ["会议重点回顾了核心设备的验收情况。Phantom VEO 710S高速相机和1000W光源系统已顺利到货并完成验收，设备的技术参数完全符合项目要求。通过与供应商的深入技术交流，项目团队对设备的性能特点和操作要点有了更深入的理解，为后续的调试工作做好了充分准备。", "", ""],
            ["", "", ""],
            ["Rocky在会上高度评价了项目团队的执行力和协作精神。在面对设备采购、工装制作、环境改造等多线并行的复杂任务时，各专业组密切配合，统筹协调，确保了各项工作的按时完成。特别是工装制作团队，在保证精度的前提下，提前完成了所有夹具的制作和验证，体现了很高的专业水准。", "", ""],
            ["", "", ""],
            ["会议确定了12月份的工作重点：一是完成HSV测试台架的精确搭建和光学系统调试；二是完成测试背景的调试和优化；三是启动HSV软件的基础配置工作。会议强调，12月份是项目从硬件建设向软件调试转换的关键时期，项目团队要做好充分的技术准备和风险预案。", "", ""],
            ["", "", ""],
            ["会议还特别强调了标准化建设的重要性。随着硬件平台的建立，项目要开始注重操作规范和质量标准的建立。要将前期积累的经验和技术要点及时总结固化，形成可复制、可推广的标准化流程，为项目的成功验收和后续推广应用奠定基础。", "", ""]
        ]
        
        # Combine all data
        all_data = header_data + task_data + personnel_data + equipment_data + summary_data
        
        # Create DataFrame
        df = pd.DataFrame(all_data)
        
        # Write to Excel
        df.to_excel(writer, sheet_name='2022年11月会议纪要', index=False, header=False)

        # Get workbook and worksheet for formatting
        workbook = writer.book
        worksheet = writer.sheets['2022年11月会议纪要']
        
        # Set column widths
        worksheet.column_dimensions['A'].width = 15
        worksheet.column_dimensions['B'].width = 50
        worksheet.column_dimensions['C'].width = 50
        
        print("Excel文件已创建：RD2204会议纪要-2022年11月25日.xlsx")

if __name__ == "__main__":
    create_november_meeting()
