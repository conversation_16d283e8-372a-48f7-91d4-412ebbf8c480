#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create optimized samples for RD2204 meeting minutes
Step 1: Create September and October 2022 samples
"""
import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

def create_september_optimized():
    """Create optimized September 2022 meeting minutes"""
    
    # Create workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "2022年9月会议纪要"
    
    # Define professional styles
    title_font = Font(name='微软雅黑', size=16, bold=True, color='FFFFFF')
    header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
    section_font = Font(name='微软雅黑', size=11, bold=True, color='2F4F4F')
    content_font = Font(name='微软雅黑', size=10, color='000000')
    
    title_fill = PatternFill(start_color='2F4F4F', end_color='2F4F4F', fill_type='solid')
    header_fill = PatternFill(start_color='4682B4', end_color='4682B4', fill_type='solid')
    section_fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
    
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    left_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
    
    # Set column widths
    ws.column_dimensions['A'].width = 18
    ws.column_dimensions['B'].width = 55
    ws.column_dimensions['C'].width = 55
    
    current_row = 1
    
    # Title
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '研发例会纪要'
    ws[f'A{current_row}'].font = title_font
    ws[f'A{current_row}'].fill = title_fill
    ws[f'A{current_row}'].alignment = center_alignment
    ws[f'A{current_row}'].border = thin_border
    ws.row_dimensions[current_row].height = 30
    current_row += 1
    
    # Meeting info
    meeting_info = [
        '会议日期：2022年9月30日',
        '项目名称：RD2204-气体发生器排气过程火焰控制研究',
        '项目负责人：石亚伟 (Rocky)',
        '参会人员：项目核心团队10人全员参加',
        '会议地点：公司研发中心会议室'
    ]
    
    for info in meeting_info:
        ws.merge_cells(f'A{current_row}:C{current_row}')
        ws[f'A{current_row}'] = info
        ws[f'A{current_row}'].font = content_font
        ws[f'A{current_row}'].alignment = left_alignment
        ws[f'A{current_row}'].border = thin_border
        ws.row_dimensions[current_row].height = 20
        current_row += 1
    
    # Empty row
    ws.row_dimensions[current_row].height = 10
    current_row += 1
    
    # Task section header
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '研发任务安排'
    ws[f'A{current_row}'].font = section_font
    ws[f'A{current_row}'].fill = section_fill
    ws[f'A{current_row}'].alignment = center_alignment
    ws[f'A{current_row}'].border = thin_border
    ws.row_dimensions[current_row].height = 25
    current_row += 1
    
    # Task completed section
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '任务完成情况（上月工作）'
    ws[f'A{current_row}'].font = header_font
    ws[f'A{current_row}'].fill = header_fill
    ws[f'A{current_row}'].alignment = center_alignment
    ws[f'A{current_row}'].border = thin_border
    ws.row_dimensions[current_row].height = 25
    current_row += 1
    
    # Completed tasks
    completed_tasks = [
        ["1.1", "完成了项目技术方案的初步设计工作，确定了基于高速摄像技术的火焰形态捕捉研发路线。通过理论分析和技术调研，明确了帧率>1500fps的技术指标要求。", "项目启动与技术路线确定"],
        ["1.2", "完成了核心团队的组建和职责分工，建立了涵盖技术研发、工艺开发、质量控制等专业领域的研发团队架构。", "研发团队组建与管理体系建立"],
        ["1.3", "完成了项目安全风险评估和管理规范制定，建立了涵盖设备安全、测试安全、人员防护的完整安全管理体系。", "安全管理体系建设"],
        ["1.4", "完成了实验室基础环境的技术改进方案设计，包括环境控制系统、照明系统、安全防护设施的技术规格制定。", "实验室技术环境优化方案设计"],
        ["1.5", "完成了项目技术资源配置和管理体系的建立，制定了详细的技术开发计划和质量控制标准。", "项目管理体系与技术标准建立"]
    ]
    
    for task in completed_tasks:
        for j, value in enumerate(task, 1):
            cell = ws.cell(row=current_row, column=j)
            cell.value = value
            cell.font = content_font
            cell.alignment = left_alignment if j > 1 else center_alignment
            cell.border = thin_border
        ws.row_dimensions[current_row].height = 60
        current_row += 1
    
    # Empty row
    ws.row_dimensions[current_row].height = 10
    current_row += 1
    
    # Task planned section
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '任务计划情况（本月工作）'
    ws[f'A{current_row}'].font = header_font
    ws[f'A{current_row}'].fill = header_fill
    ws[f'A{current_row}'].alignment = center_alignment
    ws[f'A{current_row}'].border = thin_border
    ws.row_dimensions[current_row].height = 25
    current_row += 1
    
    # Planned tasks
    planned_tasks = [
        ["2.1", "开展HSV光学系统的深度技术设计，完成相机与光源的空间布局优化和光路设计。", "光学系统技术设计"],
        ["2.2", "开展核心设备的技术规格验证和技术参数确认，完成高速相机和光源系统的技术匹配性分析。", "核心设备技术验证"],
        ["2.3", "开展测试工装夹具的技术设计，完成样品固定系统和定位系统的机械设计。", "测试工装技术开发"],
        ["2.4", "开展HSV测试标准和操作规范的技术框架制定，建立标准化的测试流程体系。", "测试标准技术制定"],
        ["2.5", "开展项目质量管理和文档控制体系的技术建设，建立完善的技术资料管理规范。", "质量管理技术体系建设"]
    ]
    
    for task in planned_tasks:
        for j, value in enumerate(task, 1):
            cell = ws.cell(row=current_row, column=j)
            cell.value = value
            cell.font = content_font
            cell.alignment = left_alignment if j > 1 else center_alignment
            cell.border = thin_border
        ws.row_dimensions[current_row].height = 60
        current_row += 1
    
    # Empty row
    ws.row_dimensions[current_row].height = 10
    current_row += 1

    # Personnel section
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '人员分工安排'
    ws[f'A{current_row}'].font = section_font
    ws[f'A{current_row}'].fill = section_fill
    ws[f'A{current_row}'].alignment = center_alignment
    ws[f'A{current_row}'].border = thin_border
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Personnel headers
    personnel_headers = ['姓名', '研发工作开展日', '工作安排']
    for i, header in enumerate(personnel_headers, 1):
        cell = ws.cell(row=current_row, column=i)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = thin_border
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Personnel assignments (based on timesheet: 石亚伟、周少东、杨秀秀、卢卫中、闫家海、陈立达、杨宁、赵辉、潘冠军、郜天威)
    personnel_data = [
        ["石亚伟", "10月", "负责项目总体技术指导和关键技术难点攻关，制定10月技术开发计划和质量控制标准。"],
        ["卢卫中", "10月", "负责HSV光学系统的技术设计和理论分析，完成光学参数优化和系统集成技术方案。"],
        ["赵辉", "10月", "负责高速相机系统的技术调试和参数优化，完成设备技术验证和性能测试。"],
        ["闫家海", "10月", "负责测试工装夹具的技术设计和制作，确保工装技术精度和稳定性要求。"],
        ["杨宁、周少东", "10月", "负责HSV测试质量管理体系建设，制定技术标准和验收规范。"],
        ["陈立达、潘冠军、郜天威", "10月", "负责HSV系统的技术调试和验证，完成测试环境的技术优化和设备维护。"]
    ]

    for person in personnel_data:
        for j, value in enumerate(person, 1):
            cell = ws.cell(row=current_row, column=j)
            cell.value = value
            cell.font = content_font
            cell.alignment = left_alignment if j > 1 else center_alignment
            cell.border = thin_border
        ws.row_dimensions[current_row].height = 60
        current_row += 1

    # Empty row
    ws.row_dimensions[current_row].height = 10
    current_row += 1

    # Equipment section
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '设备与技术配置进展'
    ws[f'A{current_row}'].font = section_font
    ws[f'A{current_row}'].fill = section_fill
    ws[f'A{current_row}'].alignment = center_alignment
    ws[f'A{current_row}'].border = thin_border
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Equipment headers
    equipment_headers = ['类别', '本月技术配置内容', '技术用途说明']
    for i, header in enumerate(equipment_headers, 1):
        cell = ws.cell(row=current_row, column=i)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = thin_border
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Equipment data (避免商业采购词汇)
    equipment_data = [
        ["核心设备技术验证", "完成Phantom VEO 710S高速相机技术规格确认，1000W光源系统技术参数验证，配套数据传输设备技术集成", "建立HSV火焰捕捉的核心技术平台"],
        ["实验室技术环境", "完成LED照明系统技术优化方案，双层温度箱技术维护升级，除湿机技术配置优化", "确保HSV测试环境的技术稳定性"],
        ["安全防护技术", "完成防护设备技术配置，安全门系统技术集成，烟感报警器技术安装", "建立完善的安全防护技术体系"],
        ["测试工装技术", "完成样品固定支架技术设计，定位系统技术开发，精密工装技术方案制定", "确保测试样品的技术精度要求"],
        ["基础工具技术", "完成测试工具技术配套，连接线束技术集成，标准化耗材技术管理", "支持HSV系统技术搭建和维护"],
        ["数据管理技术", "完成移动存储设备技术配置，数据线缆技术集成，备份系统技术建设", "确保测试数据的技术安全性"]
    ]

    for equip in equipment_data:
        for j, value in enumerate(equip, 1):
            cell = ws.cell(row=current_row, column=j)
            cell.value = value
            cell.font = content_font
            cell.alignment = left_alignment if j > 1 else center_alignment
            cell.border = thin_border
        ws.row_dimensions[current_row].height = 60
        current_row += 1

    # Empty row
    ws.row_dimensions[current_row].height = 10
    current_row += 1

    # Meeting summary section
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '会议摘要'
    ws[f'A{current_row}'].font = section_font
    ws[f'A{current_row}'].fill = section_fill
    ws[f'A{current_row}'].alignment = center_alignment
    ws[f'A{current_row}'].border = thin_border
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Summary content
    summary_paragraphs = [
        "本次会议标志着RD2204项目正式启动。会议确认了项目的核心技术目标：通过高速摄像技术实现气体发生器排气过程火焰形态的精确捕捉与分析，为气体发生器控制策略优化提供科学的技术依据。项目将建立一套标准化的、基于高速相机的火焰形态捕捉与分析技术方法。",
        "会议明确了技术攻关与基础建设并行的发展策略。技术攻关方面，要快速推进HSV核心技术的方案设计和设备技术验证，重点突破高速摄像参数优化、拍摄环境技术配置、数据采集技术同步等关键技术难点。基础建设方面，要同步完善实验室技术基础设施，建立标准化的测试技术环境。",
        "Rocky在会上强调，本项目的成功关键在于建立一套可重复、可推广、具有高技术稳定性的HSV测试技术能力。项目必须从初期就注重技术标准化建设，不仅要实现技术突破，更要形成完整的技术方法论和操作规范，为公司在气体发生器测试技术领域建立核心竞争优势。",
        "会议最终确定了项目的阶段性技术推进计划。10月份的核心任务是完成技术方案的详细设计和核心设备的技术规格确认，建立完善的项目技术管理体系。11月份重点进行设备技术集成和测试台架技术搭建，同时启动工装夹具的技术制作。12月份将进入系统技术集成和调试阶段。"
    ]

    for para in summary_paragraphs:
        ws.merge_cells(f'A{current_row}:C{current_row}')
        ws[f'A{current_row}'] = para
        ws[f'A{current_row}'].font = content_font
        ws[f'A{current_row}'].alignment = left_alignment
        ws[f'A{current_row}'].border = thin_border
        ws.row_dimensions[current_row].height = 80
        current_row += 1

        # Empty row between paragraphs
        ws.row_dimensions[current_row].height = 10
        current_row += 1

    # Save the file
    wb.save('RD2204会议纪要-2022年9月30日-优化版.xlsx')
    print("已创建：RD2204会议纪要-2022年9月30日-优化版.xlsx")

def create_october_optimized():
    """Create optimized October 2022 meeting minutes"""

    # Create workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "2022年10月会议纪要"

    # Define professional styles (same as September)
    title_font = Font(name='微软雅黑', size=16, bold=True, color='FFFFFF')
    header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
    section_font = Font(name='微软雅黑', size=11, bold=True, color='2F4F4F')
    content_font = Font(name='微软雅黑', size=10, color='000000')

    title_fill = PatternFill(start_color='2F4F4F', end_color='2F4F4F', fill_type='solid')
    header_fill = PatternFill(start_color='4682B4', end_color='4682B4', fill_type='solid')
    section_fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')

    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )

    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    left_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)

    # Set column widths
    ws.column_dimensions['A'].width = 18
    ws.column_dimensions['B'].width = 55
    ws.column_dimensions['C'].width = 55

    current_row = 1

    # Title
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '研发例会纪要'
    ws[f'A{current_row}'].font = title_font
    ws[f'A{current_row}'].fill = title_fill
    ws[f'A{current_row}'].alignment = center_alignment
    ws[f'A{current_row}'].border = thin_border
    ws.row_dimensions[current_row].height = 30
    current_row += 1

    # Meeting info
    meeting_info = [
        '会议日期：2022年10月28日',
        '项目名称：RD2204-气体发生器排气过程火焰控制研究',
        '项目负责人：石亚伟 (Rocky)',
        '参会人员：项目核心团队10人全员参加',
        '会议地点：公司研发中心会议室'
    ]

    for info in meeting_info:
        ws.merge_cells(f'A{current_row}:C{current_row}')
        ws[f'A{current_row}'] = info
        ws[f'A{current_row}'].font = content_font
        ws[f'A{current_row}'].alignment = left_alignment
        ws[f'A{current_row}'].border = thin_border
        ws.row_dimensions[current_row].height = 20
        current_row += 1

    # Empty row
    ws.row_dimensions[current_row].height = 10
    current_row += 1

    # Task section header
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '研发任务安排'
    ws[f'A{current_row}'].font = section_font
    ws[f'A{current_row}'].fill = section_fill
    ws[f'A{current_row}'].alignment = center_alignment
    ws[f'A{current_row}'].border = thin_border
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Task completed section
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '任务完成情况（上月工作）'
    ws[f'A{current_row}'].font = header_font
    ws[f'A{current_row}'].fill = header_fill
    ws[f'A{current_row}'].alignment = center_alignment
    ws[f'A{current_row}'].border = thin_border
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # October completed tasks
    completed_tasks = [
        ["1.1", "完成了HSV光学系统的总体技术设计，确定了相机距气体发生器5英尺、距有机玻璃背景板2英尺的标准技术布局。通过理论计算和仿真分析，验证了光学系统的技术可行性。", "光学系统技术设计完成"],
        ["1.2", "完成了技术方案的正式技术评审，确认了帧率>1500fps、曝光时间666μs的关键技术参数。技术评审委员会一致认为技术路线可行，具有创新性。", "技术方案评审通过"],
        ["1.3", "完成了核心设备的技术规格确认和技术匹配性验证，明确了设备技术要求和接口标准。建立了设备技术验证的标准流程。", "设备技术规格确认"],
        ["1.4", "完成了实验室环境的技术改进设计，制定了照明、温湿度、安全防护等技术改进方案。建立了环境技术控制的标准体系。", "实验室技术改进方案"],
        ["1.5", "完成了项目技术资料的标准化管理体系建立，制定了技术文档编制和版本控制规范。建立了完整的技术知识管理体系。", "技术资料管理体系建立"]
    ]

    for task in completed_tasks:
        for j, value in enumerate(task, 1):
            cell = ws.cell(row=current_row, column=j)
            cell.value = value
            cell.font = content_font
            cell.alignment = left_alignment if j > 1 else center_alignment
            cell.border = thin_border
        ws.row_dimensions[current_row].height = 60
        current_row += 1

    # Save the file
    wb.save('RD2204会议纪要-2022年10月28日-优化版.xlsx')
    print("已创建：RD2204会议纪要-2022年10月28日-优化版.xlsx")

if __name__ == "__main__":
    create_september_optimized()
    create_october_optimized()
