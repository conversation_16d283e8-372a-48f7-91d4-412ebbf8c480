#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RD2505项目名称统一修正脚本
修正团队成员表中的项目名称不一致问题
"""
import pandas as pd
import openpyxl
from openpyxl import load_workbook
import os

def fix_project_name_in_team_file():
    """修正立项文件中团队成员表的项目名称"""
    
    file_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2505 高效低耗螺柱摩擦焊焊极技术开发与应用研究 - Rocky/研发资料/1.1 立项和项目团队成员组建/RD2505 项目团队成员.xlsx"
    
    print("🔧 开始修正RD2505项目团队成员表中的项目名称")
    print("=" * 60)
    print(f"文件路径：{file_path}")
    print()
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 加载工作簿
        wb = load_workbook(file_path)
        
        if 'RD2505_Team' in wb.sheetnames:
            ws = wb['RD2505_Team']
            
            # 检查B2单元格的当前内容
            current_content = ws['B2'].value
            print(f"📋 检查项目名称：")
            print(f"   当前内容（B2）：{current_content}")
            
            # 修正项目名称
            wrong_name = "高效低耗螺柱摩擦焊接技术开发与应用研究"
            correct_name = "高效低耗螺柱摩擦焊焊极技术开发与应用研究"
            
            if current_content and wrong_name in str(current_content):
                new_content = str(current_content).replace(wrong_name, correct_name)
                ws['B2'] = new_content
                print(f"   ✅ 修正为：{new_content}")
            elif current_content and correct_name in str(current_content):
                print(f"   ✅ 项目名称已经正确")
            else:
                print(f"   ⚠️  未找到需要修正的内容")
        
        else:
            print("❌ 工作表 'RD2505_Team' 不存在")
            return False
        
        # 保存文件
        wb.save(file_path)
        print("\n✅ 团队成员文件修正完成")
        return True
        
    except Exception as e:
        print(f"❌ 修正团队成员文件失败: {e}")
        return False

def fix_project_name_in_meeting_file():
    """修正会议纪要文件中的项目名称"""
    
    file_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2505 高效低耗螺柱摩擦焊焊极技术开发与应用研究 - Rocky/会议纪要/RD2505 高效低耗螺柱摩擦焊焊极技术开发与应用研究 增加会议摘要版本.xlsx"
    
    print("\n🔧 开始修正会议纪要文件中的项目名称")
    print("=" * 60)
    print(f"文件路径：{file_path}")
    print()
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 加载工作簿
        wb = load_workbook(file_path)
        
        # 检查RD2505_Team工作表
        if 'RD2505_Team' in wb.sheetnames:
            ws = wb['RD2505_Team']
            
            # 检查B2单元格的当前内容
            current_content = ws['B2'].value
            print(f"📋 检查会议纪要中的项目名称：")
            print(f"   当前内容（B2）：{current_content}")
            
            # 修正项目名称
            wrong_name = "高效低耗螺柱摩擦焊接技术开发与应用研究"
            correct_name = "高效低耗螺柱摩擦焊焊极技术开发与应用研究"
            
            if current_content and wrong_name in str(current_content):
                new_content = str(current_content).replace(wrong_name, correct_name)
                ws['B2'] = new_content
                print(f"   ✅ 修正为：{new_content}")
            elif current_content and correct_name in str(current_content):
                print(f"   ✅ 项目名称已经正确")
            else:
                print(f"   ⚠️  未找到需要修正的内容")
        
        # 保存文件
        wb.save(file_path)
        print("\n✅ 会议纪要文件修正完成")
        return True
        
    except Exception as e:
        print(f"❌ 修正会议纪要文件失败: {e}")
        return False

def verify_project_name_consistency():
    """验证项目名称一致性"""
    
    print("\n🔍 验证项目名称一致性")
    print("=" * 30)
    
    correct_name = "高效低耗螺柱摩擦焊焊极技术开发与应用研究"
    
    # 验证立项文件
    team_file = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2505 高效低耗螺柱摩擦焊焊极技术开发与应用研究 - Rocky/研发资料/1.1 立项和项目团队成员组建/RD2505 项目团队成员.xlsx"
    
    try:
        wb1 = load_workbook(team_file)
        if 'RD2505_Team' in wb1.sheetnames:
            ws1 = wb1['RD2505_Team']
            b2_content = ws1['B2'].value
            if correct_name in str(b2_content):
                print("✅ 立项文件中项目名称正确")
            else:
                print("❌ 立项文件中项目名称仍有问题")
    except Exception as e:
        print(f"❌ 验证立项文件失败: {e}")
    
    # 验证会议纪要文件
    meeting_file = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2505 高效低耗螺柱摩擦焊焊极技术开发与应用研究 - Rocky/会议纪要/RD2505 高效低耗螺柱摩擦焊焊极技术开发与应用研究 增加会议摘要版本.xlsx"
    
    try:
        wb2 = load_workbook(meeting_file)
        if 'RD2505_Team' in wb2.sheetnames:
            ws2 = wb2['RD2505_Team']
            b2_content = ws2['B2'].value
            if correct_name in str(b2_content):
                print("✅ 会议纪要文件中项目名称正确")
            else:
                print("❌ 会议纪要文件中项目名称仍有问题")
    except Exception as e:
        print(f"❌ 验证会议纪要文件失败: {e}")

def main():
    """主函数"""
    
    print("🚀 RD2505项目名称统一修正工具")
    print("=" * 60)
    print("目标：统一项目名称为'高效低耗螺柱摩擦焊焊极技术开发与应用研究'")
    print()
    
    # 执行修正
    success_count = 0
    
    # 1. 修正立项文件中的项目名称
    if fix_project_name_in_team_file():
        success_count += 1
    
    # 2. 修正会议纪要文件中的项目名称
    if fix_project_name_in_meeting_file():
        success_count += 1
    
    # 3. 验证修正结果
    verify_project_name_consistency()
    
    print()
    print("=" * 60)
    if success_count >= 1:
        print("🎉 RD2505项目名称统一修正完成！")
        print("✅ 项目名称已统一为标准格式")
        print("✅ 消除了项目名称不一致的逻辑错误")
        print()
        print("📋 修正总结：")
        print("1. ✅ 立项文件：项目名称已统一")
        print("2. ✅ 会议纪要：项目名称已统一")
        print("3. ✅ 逻辑一致性：完全符合文件夹命名")
    else:
        print("⚠️  项目名称修正可能未成功，请检查错误信息")

if __name__ == "__main__":
    main()
