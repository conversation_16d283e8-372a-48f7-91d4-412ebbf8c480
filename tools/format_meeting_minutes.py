#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Professional formatting for RD2204 meeting minutes Excel files
"""
import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter

def create_professional_meeting_minutes():
    """Create professionally formatted meeting minutes"""
    
    # Create workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "2022年9月会议纪要"
    
    # Define styles
    title_font = Font(name='微软雅黑', size=16, bold=True, color='FFFFFF')
    header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
    section_font = Font(name='微软雅黑', size=11, bold=True, color='2F4F4F')
    content_font = Font(name='微软雅黑', size=10, color='000000')
    
    title_fill = PatternFill(start_color='2F4F4F', end_color='2F4F4F', fill_type='solid')
    header_fill = PatternFill(start_color='4682B4', end_color='4682B4', fill_type='solid')
    section_fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
    
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    left_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
    
    # Set column widths
    ws.column_dimensions['A'].width = 18
    ws.column_dimensions['B'].width = 55
    ws.column_dimensions['C'].width = 55
    
    # Title section (rows 1-2)
    ws.merge_cells('A1:C1')
    ws['A1'] = '研发例会纪要'
    ws['A1'].font = title_font
    ws['A1'].fill = title_fill
    ws['A1'].alignment = center_alignment
    ws['A1'].border = thin_border
    ws.row_dimensions[1].height = 30
    
    # Meeting info section (rows 3-7)
    meeting_info = [
        ['会议日期：2022年9月30日', '', ''],
        ['项目名称：RD2204-气体发生器排气过程火焰控制研究', '', ''],
        ['项目负责人：石亚伟 (Rocky)', '', ''],
        ['参会人员：项目核心团队14人全员参加', '', ''],
        ['会议地点：公司研发中心会议室', '', '']
    ]
    
    for i, info in enumerate(meeting_info, 3):
        ws.merge_cells(f'A{i}:C{i}')
        ws[f'A{i}'] = info[0]
        ws[f'A{i}'].font = content_font
        ws[f'A{i}'].alignment = left_alignment
        ws[f'A{i}'].border = thin_border
        ws.row_dimensions[i].height = 20
    
    # Empty row
    ws.row_dimensions[8].height = 10
    
    # Task section header (row 9)
    ws.merge_cells('A9:C9')
    ws['A9'] = '研发任务安排'
    ws['A9'].font = section_font
    ws['A9'].fill = section_fill
    ws['A9'].alignment = center_alignment
    ws['A9'].border = thin_border
    ws.row_dimensions[9].height = 25
    
    # Task table header (row 10)
    task_headers = ['序号', '任务完成情况', '任务计划情况']
    for i, header in enumerate(task_headers, 1):
        cell = ws.cell(row=10, column=i)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = thin_border
    ws.row_dimensions[10].height = 25
    
    # Task data (rows 11-15)
    task_data = [
        [1, "完成了项目正式启动与核心团队组建工作。经过充分讨论，确定了由实验室经理、技术研发工程师、工艺工程师、质量工程师、测试员和实验员组成的14人研发团队，明确了各成员在项目中的核心职责分工和协作机制。", "启动HSV测试台架的详细方案设计与硬件选型工作。重点完成光学系统布局设计、相机与光源的技术参数确认，以及测试环境的初步规划。"],
        [2, "完成了技术方案的内部评审工作，明确了基于高速摄像技术的火焰形态捕捉技术路线。通过对比分析多种技术方案，确定了采用帧率>1500fps的高速相机配合1000W可调光束光源的技术架构。", "完成核心设备的技术规格确认与采购流程启动。包括高速相机Phantom VEO 710S的技术参数验证、1000W光源系统的配套要求确认，以及相关配套设备的技术对接工作。"],
        [3, "完成了项目安全风险评估工作，制定了涵盖设备安全、测试安全、人员防护等方面的项目安全管理规范。特别针对气体发生器测试的特殊性，建立了完善的安全操作流程和应急预案。", "启动测试工装夹具的设计工作。包括气体发生器样品固定系统的机械设计、高速相机拍摄环境的配置优化，以及测试过程中样品定位精度的保证措施。"],
        [4, "建立了实验室基础设施的全面改进计划，涵盖环境控制系统优化、安全防护设施完善、测试区域布局调整等多个方面。同时完成了项目所需基础工具和标准化耗材的需求分析。", "完成HSV测试标准和操作规范的初步框架制定。建立包含测试流程、质量控制、数据记录、结果分析等环节的标准化体系，为后续测试工作提供规范指导。"],
        [5, "完成了项目预算的详细分解和资源配置计划，确保各阶段工作的资源保障。建立了项目进度跟踪机制和风险控制措施，为项目顺利推进奠定了管理基础。", "建立项目质量管理体系和文档管理规范。制定详细的项目文档编制标准、版本控制流程，以及知识管理和经验积累机制。"]
    ]

    for i, task in enumerate(task_data, 11):
        for j, value in enumerate(task, 1):
            cell = ws.cell(row=i, column=j)
            cell.value = value
            cell.font = content_font
            cell.alignment = left_alignment if j > 1 else center_alignment
            cell.border = thin_border
        ws.row_dimensions[i].height = 80

    # Add remaining sections (personnel, equipment, summary)
    current_row = 16

    # Empty row
    ws.row_dimensions[current_row].height = 10
    current_row += 1

    # Personnel section
    ws.merge_cells(f'A{current_row}:C{current_row}')
    ws[f'A{current_row}'] = '人员分工安排'
    ws[f'A{current_row}'].font = section_font
    ws[f'A{current_row}'].fill = section_fill
    ws[f'A{current_row}'].alignment = center_alignment
    ws[f'A{current_row}'].border = thin_border
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Personnel headers
    personnel_headers = ['姓名', '研发工作开展日', '工作安排']
    for i, header in enumerate(personnel_headers, 1):
        cell = ws.cell(row=current_row, column=i)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = thin_border
    ws.row_dimensions[current_row].height = 25
    current_row += 1

    # Personnel data
    personnel_data = [
        ["卢卫中", "10月", "负责HSV光学测试台架的总体方案设计与理论分析工作。包括光学系统布局优化、相机与光源的相对位置关系确定、拍摄角度和距离的理论计算，以及整体测试台架的技术参数要求制定。"],
        ["朱丰海", "10月", "负责测试方法的工艺流程设计工作。深入拆解HSV测试的详细步骤，明确各环节的关键控制点，制定标准化的操作流程，并建立测试质量控制和数据记录规范。"],
        ["赵辉", "10月", "负责高速相机和光源系统的技术调研、选型确认及供应商技术对接工作。重点完成Phantom VEO 710S高速相机的技术参数验证、1000W光源系统的配套要求确认，以及相关配套设备的技术规格对接。"]
    ]

    for person in personnel_data:
        for j, value in enumerate(person, 1):
            cell = ws.cell(row=current_row, column=j)
            cell.value = value
            cell.font = content_font
            cell.alignment = left_alignment if j > 1 else center_alignment
            cell.border = thin_border
        ws.row_dimensions[current_row].height = 60
        current_row += 1

    # Save the file
    wb.save('RD2204会议纪要-2022年9月30日-专业格式.xlsx')
    print("专业格式化的Excel文件已创建：RD2204会议纪要-2022年9月30日-专业格式.xlsx")

if __name__ == "__main__":
    create_professional_meeting_minutes()
