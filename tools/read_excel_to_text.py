#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Read an Excel (.xlsx) file and print structured text for all sheets.
Requirements: openpyxl
Usage: python3 tools/read_excel_to_text.py "<excel_path>"
Output:
- For each sheet: prints sheet name and the computed data range
- Prints non-empty cells with their coordinates and values
- Preserves row/column order
"""
import sys
import os
from datetime import datetime, date, time
from typing import Optional

from openpyxl import load_workbook
from openpyxl.cell.cell import Cell


def format_cell_value(cell: Cell) -> str:
    v = cell.value
    if v is None:
        return ""
    # Date/Datetime
    if isinstance(v, (datetime, date)):
        # Standard ISO format
        return v.isoformat()
    # Time
    if isinstance(v, time):
        return v.strftime("%H:%M:%S")
    # Numbers / others: cast to str safely
    try:
        return str(v)
    except Exception:
        return repr(v)


def compute_data_bounds(ws) -> Optional[tuple[int, int, int, int]]:
    """Compute min_row, max_row, min_col, max_col that bound non-empty cells."""
    min_row = min_col = 10**9
    max_row = max_col = 0
    for row in ws.iter_rows(values_only=False):
        for c in row:
            if c.value not in (None, ""):
                r = c.row
                col = c.column
                if r < min_row:
                    min_row = r
                if r > max_row:
                    max_row = r
                if col < min_col:
                    min_col = col
                if col > max_col:
                    max_col = col
    if max_row == 0:
        return None
    return (min_row, max_row, min_col, max_col)


def colnum_to_name(n: int) -> str:
    """1 -> A, 2 -> B, ..."""
    name = []
    while n > 0:
        n, r = divmod(n - 1, 26)
        name.append(chr(65 + r))
    return "".join(reversed(name))


def main():
    if len(sys.argv) < 2:
        print("Usage: python3 tools/read_excel_to_text.py <excel_path>", file=sys.stderr)
        sys.exit(2)
    path = sys.argv[1]
    if not os.path.exists(path):
        print(f"ERROR: file not found: {path}", file=sys.stderr)
        sys.exit(1)

    try:
        wb = load_workbook(filename=path, data_only=True, read_only=True)
    except Exception as e:
        print(f"ERROR: failed to load workbook: {e}", file=sys.stderr)
        sys.exit(3)

    print(f"FILE: {path}")
    print(f"SHEETS: {wb.sheetnames}")

    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        bounds = compute_data_bounds(ws)
        print("=" * 80)
        print(f"SHEET: {sheet_name}")
        if bounds is None:
            print("DATA_RANGE: <empty>")
            continue
        min_row, max_row, min_col, max_col = bounds
        print(f"DATA_RANGE: rows {min_row}-{max_row}, cols {min_col}-{max_col} ({colnum_to_name(min_col)}-{colnum_to_name(max_col)})")
        # Iterate preserving row-major order
        for r in range(min_row, max_row + 1):
            row_items = []
            for c in range(min_col, max_col + 1):
                cell = ws.cell(row=r, column=c)
                if cell.value in (None, ""):
                    continue
                coord = f"{colnum_to_name(c)}{r}"
                row_items.append(f"{coord}={format_cell_value(cell)}")
            if row_items:
                # Keep relative row grouping
                print(" | ".join(row_items))


if __name__ == "__main__":
    main()

