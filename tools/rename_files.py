#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RD2201项目文件重命名脚本
按优先级执行文件和文件夹重命名操作
"""
import os
import shutil

def rename_folders():
    """重命名文件夹 - 去除中文顿号"""
    
    base_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2201 混合式气体发生器内部工作压力测试方法研究 - Rocky/研发资料"
    
    folder_renames = [
        ("2.1、技术方案和操作步骤", "2.1 技术方案和操作步骤"),
        ("2.2、操作步骤", "2.2 操作步骤"),
        ("4.1、工艺验证", "4.1 工艺验证"),
        ("4.2、表单更新", "4.2 表单更新"),
        ("4.3、备件耗材清单", "4.3 备件耗材清单"),
        ("5.1、文件更新记录", "5.1 文件更新记录")
    ]
    
    print("=== 开始重命名文件夹 ===")
    
    for old_name, new_name in folder_renames:
        old_path = os.path.join(base_path, old_name)
        new_path = os.path.join(base_path, new_name)
        
        if os.path.exists(old_path):
            try:
                os.rename(old_path, new_path)
                print(f"✅ 成功重命名: {old_name} → {new_name}")
            except Exception as e:
                print(f"❌ 重命名失败: {old_name} - 错误: {e}")
        else:
            print(f"⚠️  文件夹不存在: {old_name}")
    
    print()

def rename_image_files():
    """重命名图片文件 - 解决时间戳问题"""
    
    base_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2201 混合式气体发生器内部工作压力测试方法研究 - Rocky/研发资料/2.1 技术方案和操作步骤"
    
    # 图片文件重命名映射
    image_renames = [
        ("微信图片_20200827163521.jpg", "IOP测试现场图片_001.jpg"),
        ("微信图片_202008271635211.jpg", "IOP测试现场图片_002.jpg"),
        ("微信图片_2020082716352110.jpg", "测试设备布局图_001.jpg"),
        ("微信图片_2020082716352111.jpg", "测试设备布局图_002.jpg"),
        ("微信图片_2020082716352112.jpg", "安全防护装置图_001.jpg"),
        ("微信图片_2020082716352113.jpg", "安全防护装置图_002.jpg"),
        ("微信图片_2020082716352114.jpg", "工艺流程图_001.jpg"),
        ("微信图片_202008271635212.jpg", "工艺流程图_002.jpg"),
        ("微信图片_202008271635213.jpg", "测试数据记录图_001.jpg"),
        ("微信图片_202008271635214.jpg", "测试数据记录图_002.jpg"),
        ("微信图片_202008271635215.jpg", "设备连接示意图_001.jpg"),
        ("微信图片_202008271635216.jpg", "设备连接示意图_002.jpg"),
        ("微信图片_202008271635217.jpg", "压力传感器安装图_001.jpg"),
        ("微信图片_202008271635218.jpg", "压力传感器安装图_002.jpg"),
        ("微信图片_202008271635219.jpg", "测试结果分析图_001.jpg"),
        ("微信图片_2020083023241717.jpg", "测试结果分析图_002.jpg"),
        ("微信图片_2020083023241720.jpg", "质量控制检查图_001.jpg")
    ]
    
    print("=== 开始重命名图片文件 ===")
    
    if not os.path.exists(base_path):
        print(f"❌ 路径不存在: {base_path}")
        return
    
    for old_name, new_name in image_renames:
        old_path = os.path.join(base_path, old_name)
        new_path = os.path.join(base_path, new_name)
        
        if os.path.exists(old_path):
            try:
                os.rename(old_path, new_path)
                print(f"✅ 成功重命名: {old_name} → {new_name}")
            except Exception as e:
                print(f"❌ 重命名失败: {old_name} - 错误: {e}")
        else:
            print(f"⚠️  文件不存在: {old_name}")
    
    print()

def rename_excel_files():
    """重命名Excel文件 - 格式规范化"""
    
    base_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2201 混合式气体发生器内部工作压力测试方法研究 - Rocky/研发资料/3.2 测试数据模板和数据收集"
    
    excel_renames = [
        ("3.2.1、CH5-35 HM IOP测试数据.xlsx", "3.2.1 CH5-35HM IOP测试数据.xlsx"),
        ("3.2.2、G2P HM IOP测试数据.xlsx", "3.2.2 G2P HM IOP测试数据.xlsx"),
        ("3.2.3、CH5-30 IOP测试数据.xlsx", "3.2.3 CH5-30 IOP测试数据.xlsx"),
        ("TS  柱状 IOP测试表格模板.xlsx", "TS柱状IOP测试表格模板.xlsx")
    ]
    
    print("=== 开始重命名Excel文件 ===")
    
    if not os.path.exists(base_path):
        print(f"❌ 路径不存在: {base_path}")
        return
    
    for old_name, new_name in excel_renames:
        old_path = os.path.join(base_path, old_name)
        new_path = os.path.join(base_path, new_name)
        
        if os.path.exists(old_path):
            try:
                os.rename(old_path, new_path)
                print(f"✅ 成功重命名: {old_name} → {new_name}")
            except Exception as e:
                print(f"❌ 重命名失败: {old_name} - 错误: {e}")
        else:
            print(f"⚠️  文件不存在: {old_name}")
    
    print()

def main():
    """主函数 - 按优先级执行重命名操作"""
    
    print("🚀 开始执行RD2201项目文件重命名操作")
    print("=" * 50)
    
    # 第一阶段：高优先级文件夹重命名
    print("📁 第一阶段：文件夹重命名（高优先级）")
    rename_folders()
    
    # 第二阶段：高优先级图片文件重命名
    print("🖼️  第二阶段：图片文件重命名（高优先级）")
    rename_image_files()
    
    # 第三阶段：中优先级Excel文件重命名
    print("📊 第三阶段：Excel文件重命名（中优先级）")
    rename_excel_files()
    
    print("=" * 50)
    print("✅ 文件重命名操作完成！")
    print()
    print("📋 下一步建议：")
    print("1. 检查重命名结果")
    print("2. 继续执行Excel内容修改")
    print("3. 验证所有修改的正确性")

if __name__ == "__main__":
    main()
