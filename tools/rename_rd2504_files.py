#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RD2504项目文件重命名脚本
统一文件命名格式为标准格式
"""
import os

def rename_rd2504_files():
    """重命名RD2504项目中的文件"""
    
    print("🔧 开始RD2504项目文件重命名")
    print("=" * 50)
    
    target_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2504 15000PSI高压力压力循环测试技术开发 - Rocky/研发资料/2.1 安全风险评估 - 噪音测量"
    
    # 定义重命名映射
    file_renames = [
        ("1 - 噪音测试收集数据.jpg", "噪音测试数据收集_001.jpg"),
        ("2 - 噪音测试收集数据.jpg", "噪音测试数据收集_002.jpg"),
        ("3 - 噪音测试收集视频.mp4", "噪音测试数据收集_001.mp4")
    ]
    
    print(f"目标路径：{target_path}")
    print()
    
    if not os.path.exists(target_path):
        print(f"❌ 路径不存在: {target_path}")
        return False
    
    success_count = 0
    
    for old_name, new_name in file_renames:
        old_path = os.path.join(target_path, old_name)
        new_path = os.path.join(target_path, new_name)
        
        print(f"📋 处理文件：{old_name}")
        
        if os.path.exists(old_path):
            try:
                os.rename(old_path, new_path)
                print(f"   ✅ 成功重命名为：{new_name}")
                success_count += 1
            except Exception as e:
                print(f"   ❌ 重命名失败：{e}")
        else:
            print(f"   ⚠️  文件不存在")
    
    print()
    print(f"📊 重命名结果：")
    print(f"   成功重命名：{success_count}/{len(file_renames)} 个文件")
    
    if success_count == len(file_renames):
        print("✅ 所有文件重命名成功！")
        return True
    else:
        print("⚠️  部分文件重命名失败")
        return False

def verify_naming():
    """验证重命名结果"""
    
    print("\n🔍 验证重命名结果")
    print("=" * 30)
    
    target_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2504 15000PSI高压力压力循环测试技术开发 - Rocky/研发资料/2.1 安全风险评估 - 噪音测量"
    
    if not os.path.exists(target_path):
        print(f"❌ 路径不存在")
        return
    
    files = [f for f in os.listdir(target_path) if os.path.isfile(os.path.join(target_path, f))]
    
    print("📁 当前文件列表：")
    for file in sorted(files):
        if file.startswith("噪音测试数据收集_"):
            print(f"   ✅ {file} (标准格式)")
        else:
            print(f"   ⚠️  {file} (需要检查)")
    
    # 检查是否符合标准格式
    expected_files = [
        "噪音测试数据收集_001.jpg",
        "噪音测试数据收集_002.jpg", 
        "噪音测试数据收集_001.mp4"
    ]
    
    all_correct = True
    for expected_file in expected_files:
        if expected_file in files:
            print(f"✅ 找到标准格式文件：{expected_file}")
        else:
            print(f"❌ 缺少标准格式文件：{expected_file}")
            all_correct = False
    
    if all_correct:
        print("\n🎉 文件命名格式完全符合标准！")
    else:
        print("\n⚠️  文件命名格式需要进一步调整")

def main():
    """主函数"""
    
    print("🚀 RD2504项目文件命名标准化工具")
    print("=" * 60)
    print("目标：将文件重命名为标准格式 [功能描述]_[序号].[扩展名]")
    print()
    
    # 执行重命名
    success = rename_rd2504_files()
    
    # 验证结果
    verify_naming()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 RD2504项目文件重命名完成！")
        print("✅ 文件命名格式已统一为标准格式")
        print("✅ 符合高新技术企业审核要求")
    else:
        print("⚠️  文件重命名部分完成，请检查错误信息")

if __name__ == "__main__":
    main()
