#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RD2505项目文件重命名脚本
统一技术资料文件的命名格式为标准格式
"""
import os

def rename_rd2505_files():
    """重命名RD2505项目中的文件"""
    
    print("🔧 开始RD2505项目文件重命名")
    print("=" * 50)
    
    target_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2505 高效低耗螺柱摩擦焊焊极技术开发与应用研究 - Rocky/研发资料"
    
    # 定义重命名映射
    file_renames = [
        ("样件外观确认 (17).jpg", "样件外观确认_017.jpg"),
        ("样件外观确认.jpg", "样件外观确认_001.jpg"),
        ("样件外观确认2.jpg", "样件外观确认_002.jpg"),
        ("设备试制时状态记录 (1).jpg", "设备试制记录_001.jpg"),
        ("设备试制时状态记录 (1).mp4", "设备试制记录_001.mp4"),
        ("设备试制时状态记录 - 样件外观确认 (1).jpg", "样件外观确认_003.jpg"),
        ("设备试制时状态记录 2.jpg", "设备试制记录_002.jpg")
    ]
    
    print(f"目标路径：{target_path}")
    print()
    
    if not os.path.exists(target_path):
        print(f"❌ 路径不存在: {target_path}")
        return False
    
    success_count = 0
    
    for old_name, new_name in file_renames:
        old_path = os.path.join(target_path, old_name)
        new_path = os.path.join(target_path, new_name)
        
        print(f"📋 处理文件：{old_name}")
        
        if os.path.exists(old_path):
            try:
                # 检查新文件名是否已存在
                if os.path.exists(new_path):
                    print(f"   ⚠️  目标文件名已存在，跳过重命名")
                    continue
                
                os.rename(old_path, new_path)
                print(f"   ✅ 成功重命名为：{new_name}")
                success_count += 1
            except Exception as e:
                print(f"   ❌ 重命名失败：{e}")
        else:
            print(f"   ⚠️  文件不存在")
    
    print()
    print(f"📊 重命名结果：")
    print(f"   成功重命名：{success_count}/{len(file_renames)} 个文件")
    
    return success_count > 0

def verify_naming():
    """验证重命名结果"""
    
    print("\n🔍 验证重命名结果")
    print("=" * 30)
    
    target_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2505 高效低耗螺柱摩擦焊焊极技术开发与应用研究 - Rocky/研发资料"
    
    if not os.path.exists(target_path):
        print(f"❌ 路径不存在")
        return
    
    files = [f for f in os.listdir(target_path) if os.path.isfile(os.path.join(target_path, f))]
    
    print("📁 当前文件列表：")
    standard_files = []
    non_standard_files = []
    
    for file in sorted(files):
        if ("_" in file and file.split("_")[-1].split(".")[0].isdigit() and len(file.split("_")[-1].split(".")[0]) == 3):
            standard_files.append(file)
            print(f"   ✅ {file} (标准格式)")
        elif file.endswith(('.jpg', '.mp4', '.png')):
            non_standard_files.append(file)
            print(f"   ⚠️  {file} (非标准格式)")
        else:
            print(f"   📄 {file} (文档文件)")
    
    print(f"\n📊 文件命名统计：")
    print(f"   标准格式文件：{len(standard_files)} 个")
    print(f"   非标准格式文件：{len(non_standard_files)} 个")
    
    if len(non_standard_files) == 0:
        print("\n🎉 所有媒体文件命名格式都符合标准！")
    else:
        print(f"\n⚠️  还有 {len(non_standard_files)} 个文件需要调整命名格式")

def clean_temp_files():
    """清理临时文件"""
    
    print("\n🧹 清理临时文件")
    print("=" * 20)
    
    target_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2505 高效低耗螺柱摩擦焊焊极技术开发与应用研究 - Rocky/研发资料/1.1 立项和项目团队成员组建"
    
    temp_files = [
        "~$RD2505 项目团队成员.xlsx"
    ]
    
    for temp_file in temp_files:
        temp_path = os.path.join(target_path, temp_file)
        if os.path.exists(temp_path):
            try:
                os.remove(temp_path)
                print(f"   ✅ 删除临时文件：{temp_file}")
            except Exception as e:
                print(f"   ❌ 删除失败：{e}")
        else:
            print(f"   ℹ️  临时文件不存在：{temp_file}")

def main():
    """主函数"""
    
    print("🚀 RD2505项目文件命名标准化工具")
    print("=" * 60)
    print("目标：将文件重命名为标准格式 [功能描述]_[序号].[扩展名]")
    print()
    
    # 执行重命名
    success = rename_rd2505_files()
    
    # 验证结果
    verify_naming()
    
    # 清理临时文件
    clean_temp_files()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 RD2505项目文件重命名完成！")
        print("✅ 文件命名格式已统一为标准格式")
        print("✅ 符合高新技术企业审核要求")
        print()
        print("📋 修正总结：")
        print("1. ✅ 样件外观确认文件：已统一命名格式")
        print("2. ✅ 设备试制记录文件：已统一命名格式")
        print("3. ✅ 临时文件：已清理")
        print("4. ✅ 命名规范：符合 [功能描述]_[序号] 标准")
    else:
        print("⚠️  文件重命名部分完成，请检查错误信息")

if __name__ == "__main__":
    main()
