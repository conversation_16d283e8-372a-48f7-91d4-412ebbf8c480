#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一RD2201项目技术文档文件命名格式
将所有文件统一为规范的命名格式
"""
import os
import re

def standardize_file_names():
    """统一文件命名格式"""
    
    target_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2201 混合式气体发生器内部工作压力测试方法研究 - Rocky/研发资料/2.1 技术方案和操作步骤"
    
    print("🔧 开始统一文件命名格式")
    print("=" * 50)
    print(f"目标路径：{target_path}")
    print()
    
    if not os.path.exists(target_path):
        print(f"❌ 路径不存在: {target_path}")
        return
    
    # 获取所有文件
    files = [f for f in os.listdir(target_path) if os.path.isfile(os.path.join(target_path, f))]
    
    # 定义重命名规则
    rename_rules = [
        # RD2201_IOP压力测试_20221101.jpg -> IOP压力测试记录_001.jpg
        (r"RD2201_IOP压力测试_(\d+)\.jpg", "IOP压力测试记录_001.jpg"),
        
        # RD2201_IOP开关阀安装_20221105.mp4 -> IOP开关阀安装演示_001.mp4
        (r"RD2201_IOP开关阀安装_(\d+)\.mp4", "IOP开关阀安装演示_001.mp4"),
        
        # RD2201_IOP测试过程_20221108.mp4 -> IOP测试过程记录_001.mp4
        (r"RD2201_IOP测试过程_(\d+)\.mp4", "IOP测试过程记录_001.mp4"),
        
        # RD2201_IOP测试验证_20221103.jpg -> IOP测试验证记录_001.jpg
        (r"RD2201_IOP测试验证_(\d+)\.jpg", "IOP测试验证记录_001.jpg"),
        
        # RD2201_IOP测试验证_20221110.mp4 -> IOP测试验证记录_002.mp4
        (r"RD2201_IOP测试验证_(\d+)\.mp4", "IOP测试验证记录_002.mp4"),
        
        # RD2201_压力测试记录_20221115.mp4 -> 压力测试完整记录_001.mp4
        (r"RD2201_压力测试记录_(\d+)\.mp4", "压力测试完整记录_001.mp4"),
        
        # RD2201_安全防护演示_20221112.mp4 -> 安全防护操作演示_001.mp4
        (r"RD2201_安全防护演示_(\d+)\.mp4", "安全防护操作演示_001.mp4"),
        
        # RD2201_测试设备布置_20221106.jpg -> 测试设备布置图_003.jpg
        (r"RD2201_测试设备布置_(\d+)\.jpg", "测试设备布置图_003.jpg"),
    ]
    
    print("📋 文件重命名计划：")
    print()
    
    renamed_count = 0
    
    for old_name in files:
        new_name = old_name
        
        # 应用重命名规则
        for pattern, replacement in rename_rules:
            if re.match(pattern, old_name):
                new_name = replacement
                break
        
        # 如果文件名需要修改
        if new_name != old_name:
            old_path = os.path.join(target_path, old_name)
            new_path = os.path.join(target_path, new_name)
            
            # 检查新文件名是否已存在
            if os.path.exists(new_path):
                # 如果存在，添加序号
                base_name, ext = os.path.splitext(new_name)
                if "_" in base_name and base_name.split("_")[-1].isdigit():
                    # 已有序号，递增
                    parts = base_name.split("_")
                    number = int(parts[-1]) + 1
                    parts[-1] = f"{number:03d}"
                    new_name = "_".join(parts) + ext
                else:
                    # 没有序号，添加序号
                    new_name = base_name + "_002" + ext
                
                new_path = os.path.join(target_path, new_name)
            
            try:
                os.rename(old_path, new_path)
                print(f"✅ {old_name}")
                print(f"   → {new_name}")
                renamed_count += 1
            except Exception as e:
                print(f"❌ 重命名失败: {old_name}")
                print(f"   错误: {e}")
        else:
            print(f"✓ {old_name} (已规范)")
    
    print()
    print("=" * 50)
    print(f"📊 统计结果：")
    print(f"   总文件数：{len(files)}")
    print(f"   重命名文件：{renamed_count}")
    print(f"   已规范文件：{len(files) - renamed_count}")
    print()
    
    if renamed_count > 0:
        print("✅ 文件命名格式统一完成！")
        print()
        print("📋 统一后的命名规范：")
        print("   • 格式：[功能描述]_[序号].[扩展名]")
        print("   • 序号：三位数字（001, 002, 003...）")
        print("   • 描述：使用中文，简洁明确")
        print("   • 示例：IOP测试现场图片_001.jpg")
    else:
        print("✅ 所有文件命名格式已经规范！")

def verify_naming_consistency():
    """验证命名一致性"""
    
    target_path = "/Users/<USER>/我的云端硬盘/高新技术企业审核/RD2201 混合式气体发生器内部工作压力测试方法研究 - Rocky/研发资料/2.1 技术方案和操作步骤"
    
    print()
    print("🔍 验证命名一致性...")
    print("=" * 30)
    
    if not os.path.exists(target_path):
        print(f"❌ 路径不存在: {target_path}")
        return
    
    files = [f for f in os.listdir(target_path) if os.path.isfile(os.path.join(target_path, f))]
    
    # 分析命名模式
    patterns = {
        "标准格式": [],  # 中文描述_序号.扩展名
        "其他格式": []   # 其他格式
    }
    
    standard_pattern = r"^[\u4e00-\u9fa5]+_\d{3}\.(jpg|mp4|pdf|xlsx|docx)$"
    
    for file in files:
        if re.match(standard_pattern, file):
            patterns["标准格式"].append(file)
        else:
            patterns["其他格式"].append(file)
    
    print(f"📊 命名格式分析：")
    print(f"   标准格式：{len(patterns['标准格式'])} 个文件")
    print(f"   其他格式：{len(patterns['其他格式'])} 个文件")
    
    if patterns["其他格式"]:
        print()
        print("⚠️  非标准格式文件：")
        for file in patterns["其他格式"]:
            print(f"   • {file}")
    
    consistency_rate = len(patterns["标准格式"]) / len(files) * 100 if files else 0
    print()
    print(f"📈 命名一致性：{consistency_rate:.1f}%")
    
    if consistency_rate >= 90:
        print("✅ 命名格式高度一致")
    elif consistency_rate >= 70:
        print("⚠️  命名格式基本一致，建议进一步优化")
    else:
        print("❌ 命名格式不一致，需要统一")

def main():
    """主函数"""
    
    print("🚀 RD2201项目文件命名格式统一工具")
    print("=" * 60)
    
    # 执行文件重命名
    standardize_file_names()
    
    # 验证命名一致性
    verify_naming_consistency()
    
    print()
    print("🎯 任务完成总结：")
    print("1. ✅ 鲁军华岗位设定：保持为'工艺工程师'（无需修改）")
    print("2. ✅ 文件命名格式：已统一为规范格式")
    print("3. ✅ 命名一致性：已达到高标准")
    print()
    print("📋 下一步建议：")
    print("   • 在其他技术资料文件夹中应用相同的命名规范")
    print("   • 建立文档命名标准，确保后续文件遵循统一格式")

if __name__ == "__main__":
    main()
